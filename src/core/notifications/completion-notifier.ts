/**
 * Completion Notifier
 * Handles notifications for workflow completion events
 */

export interface NotificationConfig {
  email?: {
    enabled: boolean;
    recipients: string[];
    template?: string;
    subject?: string;
  };
  webhook?: {
    enabled: boolean;
    urls: string[];
    retryAttempts?: number;
    timeout?: number;
  };
  slack?: {
    enabled: boolean;
    webhookUrl?: string;
    channel?: string;
  };
  internal?: {
    enabled: boolean;
    logLevel?: 'info' | 'warn' | 'error';
  };
}

export interface NotificationPayload {
  executionId: string;
  workflowId: string;
  status: 'completed' | 'failed' | 'cancelled';
  timestamp: string;
  summary: {
    stepsCompleted: number;
    totalSteps: number;
    duration: number;
    successRate: number;
  };
  outputs?: {
    finalContent?: boolean;
    cmsPublished?: boolean;
    cmsPostId?: string;
    qualityScore?: number;
  };
  links?: {
    resultsUrl?: string;
    cmsUrl?: string;
    downloadUrls?: string[];
  };
}

export interface NotificationResult {
  type: 'email' | 'webhook' | 'slack' | 'internal';
  recipient: string;
  status: 'sent' | 'failed' | 'pending';
  timestamp: string;
  message?: string;
  error?: string;
  retryCount?: number;
}

export class CompletionNotifier {
  private config: NotificationConfig;

  constructor(config: NotificationConfig = {}) {
    this.config = {
      email: { enabled: false, recipients: [] },
      webhook: { enabled: false, urls: [], retryAttempts: 3, timeout: 5000 },
      slack: { enabled: false },
      internal: { enabled: true, logLevel: 'info' },
      ...config
    };
  }

  /**
   * Send all configured notifications
   */
  async sendNotifications(payload: NotificationPayload): Promise<NotificationResult[]> {
    console.log(`📢 Sending completion notifications for execution ${payload.executionId}`);

    const results: NotificationResult[] = [];

    // Send email notifications
    if (this.config.email?.enabled && this.config.email.recipients.length > 0) {
      const emailResults = await this.sendEmailNotifications(payload);
      results.push(...emailResults);
    }

    // Send webhook notifications
    if (this.config.webhook?.enabled && this.config.webhook.urls.length > 0) {
      const webhookResults = await this.sendWebhookNotifications(payload);
      results.push(...webhookResults);
    }

    // Send Slack notifications
    if (this.config.slack?.enabled && this.config.slack.webhookUrl) {
      const slackResult = await this.sendSlackNotification(payload);
      results.push(slackResult);
    }

    // Send internal notifications
    if (this.config.internal?.enabled) {
      const internalResult = this.sendInternalNotification(payload);
      results.push(internalResult);
    }

    console.log(`📢 Sent ${results.length} notifications for execution ${payload.executionId}`);
    return results;
  }

  /**
   * Send email notifications
   */
  private async sendEmailNotifications(payload: NotificationPayload): Promise<NotificationResult[]> {
    const results: NotificationResult[] = [];

    for (const recipient of this.config.email!.recipients) {
      try {
        await this.sendEmail(recipient, payload);
        results.push({
          type: 'email',
          recipient,
          status: 'sent',
          timestamp: new Date().toISOString(),
          message: 'Email notification sent successfully'
        });
      } catch (error) {
        results.push({
          type: 'email',
          recipient,
          status: 'failed',
          timestamp: new Date().toISOString(),
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Send webhook notifications
   */
  private async sendWebhookNotifications(payload: NotificationPayload): Promise<NotificationResult[]> {
    const results: NotificationResult[] = [];

    for (const url of this.config.webhook!.urls) {
      let retryCount = 0;
      let success = false;
      let lastError: string | undefined;

      while (retryCount <= (this.config.webhook!.retryAttempts || 3) && !success) {
        try {
          await this.sendWebhook(url, payload);
          success = true;
          results.push({
            type: 'webhook',
            recipient: url,
            status: 'sent',
            timestamp: new Date().toISOString(),
            message: 'Webhook notification sent successfully',
            retryCount
          });
        } catch (error) {
          lastError = error.message;
          retryCount++;
          
          if (retryCount <= (this.config.webhook!.retryAttempts || 3)) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          }
        }
      }

      if (!success) {
        results.push({
          type: 'webhook',
          recipient: url,
          status: 'failed',
          timestamp: new Date().toISOString(),
          error: lastError,
          retryCount
        });
      }
    }

    return results;
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(payload: NotificationPayload): Promise<NotificationResult> {
    try {
      await this.sendSlack(payload);
      return {
        type: 'slack',
        recipient: this.config.slack!.channel || 'default',
        status: 'sent',
        timestamp: new Date().toISOString(),
        message: 'Slack notification sent successfully'
      };
    } catch (error) {
      return {
        type: 'slack',
        recipient: this.config.slack!.channel || 'default',
        status: 'failed',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Send internal notification
   */
  private sendInternalNotification(payload: NotificationPayload): NotificationResult {
    const message = this.formatInternalMessage(payload);
    
    switch (this.config.internal!.logLevel) {
      case 'error':
        if (payload.status === 'failed') {
          console.error(message);
        }
        break;
      case 'warn':
        if (payload.status !== 'completed') {
          console.warn(message);
        }
        break;
      case 'info':
      default:
        console.log(message);
        break;
    }

    return {
      type: 'internal',
      recipient: 'system',
      status: 'sent',
      timestamp: new Date().toISOString(),
      message: 'Internal notification logged'
    };
  }

  /**
   * Send email (implementation placeholder)
   */
  private async sendEmail(recipient: string, payload: NotificationPayload): Promise<void> {
    console.log(`📧 Sending email to ${recipient} for execution ${payload.executionId}`);
    
    const subject = this.config.email?.subject || `Workflow ${payload.status}: ${payload.executionId}`;
    const body = this.formatEmailBody(payload);

    // Implementation would use actual email service (SendGrid, AWS SES, etc.)
    console.log(`📧 Email subject: ${subject}`);
    console.log(`📧 Email body: ${body}`);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Send webhook (implementation)
   */
  private async sendWebhook(url: string, payload: NotificationPayload): Promise<void> {
    console.log(`🔗 Sending webhook to ${url} for execution ${payload.executionId}`);

    const webhookPayload = {
      event: 'workflow.completed',
      data: payload,
      timestamp: new Date().toISOString()
    };

    // Implementation would use fetch or axios
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AuthencioCMS-Workflow/1.0'
        },
        body: JSON.stringify(webhookPayload),
        signal: AbortSignal.timeout(this.config.webhook!.timeout || 5000)
      });

      if (!response.ok) {
        throw new Error(`Webhook failed with status ${response.status}: ${response.statusText}`);
      }

      console.log(`🔗 Webhook sent successfully to ${url}`);
    } catch (error) {
      console.error(`🔗 Webhook failed for ${url}:`, error);
      throw error;
    }
  }

  /**
   * Send Slack notification (implementation placeholder)
   */
  private async sendSlack(payload: NotificationPayload): Promise<void> {
    console.log(`💬 Sending Slack notification for execution ${payload.executionId}`);

    const slackMessage = this.formatSlackMessage(payload);

    // Implementation would use Slack webhook
    console.log(`💬 Slack message: ${slackMessage}`);
    
    // Simulate Slack sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Format email body
   */
  private formatEmailBody(payload: NotificationPayload): string {
    const statusEmoji = payload.status === 'completed' ? '✅' : payload.status === 'failed' ? '❌' : '⚠️';
    
    return `
${statusEmoji} Workflow ${payload.status.toUpperCase()}

Execution ID: ${payload.executionId}
Workflow ID: ${payload.workflowId}
Completed At: ${new Date(payload.timestamp).toLocaleString()}
Duration: ${Math.round(payload.summary.duration / 1000)}s

Summary:
- Steps Completed: ${payload.summary.stepsCompleted}/${payload.summary.totalSteps}
- Success Rate: ${payload.summary.successRate.toFixed(1)}%
${payload.outputs?.qualityScore ? `- Quality Score: ${payload.outputs.qualityScore}/100` : ''}
${payload.outputs?.cmsPublished ? `- Published to CMS: ${payload.outputs.cmsPostId}` : ''}

${payload.links?.resultsUrl ? `View Results: ${payload.links.resultsUrl}` : ''}
${payload.links?.cmsUrl ? `View Published Content: ${payload.links.cmsUrl}` : ''}
    `.trim();
  }

  /**
   * Format Slack message
   */
  private formatSlackMessage(payload: NotificationPayload): string {
    const statusEmoji = payload.status === 'completed' ? ':white_check_mark:' : 
                       payload.status === 'failed' ? ':x:' : ':warning:';
    
    return `${statusEmoji} *Workflow ${payload.status.toUpperCase()}*\n` +
           `Execution: \`${payload.executionId}\`\n` +
           `Steps: ${payload.summary.stepsCompleted}/${payload.summary.totalSteps} ` +
           `(${payload.summary.successRate.toFixed(1)}%)\n` +
           `Duration: ${Math.round(payload.summary.duration / 1000)}s`;
  }

  /**
   * Format internal message
   */
  private formatInternalMessage(payload: NotificationPayload): string {
    const statusEmoji = payload.status === 'completed' ? '✅' : 
                       payload.status === 'failed' ? '❌' : '⚠️';
    
    return `${statusEmoji} Workflow ${payload.status}: ${payload.executionId} ` +
           `(${payload.summary.stepsCompleted}/${payload.summary.totalSteps} steps, ` +
           `${Math.round(payload.summary.duration / 1000)}s)`;
  }

  /**
   * Update notification configuration
   */
  updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }

  /**
   * Test notifications
   */
  async testNotifications(): Promise<NotificationResult[]> {
    const testPayload: NotificationPayload = {
      executionId: 'test-execution',
      workflowId: 'test-workflow',
      status: 'completed',
      timestamp: new Date().toISOString(),
      summary: {
        stepsCompleted: 5,
        totalSteps: 5,
        duration: 30000,
        successRate: 100
      },
      outputs: {
        finalContent: true,
        cmsPublished: true,
        cmsPostId: 'test-post-123',
        qualityScore: 85
      },
      links: {
        resultsUrl: '/workflow/results/test-execution',
        cmsUrl: '/blog/test-post-123'
      }
    };

    return await this.sendNotifications(testPayload);
  }
}
