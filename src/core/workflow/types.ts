/**
 * Workflow Engine Types
 * Simplified workflow system for content generation
 */

// Core Workflow Types
export interface Workflow {
  id: string;
  name: string;
  description: string;
  version: string;
  steps: WorkflowStep[];
  metadata: WorkflowMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: StepType;
  config: StepConfig;
  inputs: string[]; // Input variable names
  outputs: string[]; // Output variable names
  dependencies: string[]; // Step IDs this step depends on
  condition?: StepCondition; // Optional condition for execution
  branches?: ConditionalBranch[]; // Conditional branching logic
  retryConfig?: RetryConfig; // Retry configuration for failed steps
  consultationConfig?: AgentConsultationConfig; // Agent consultation configuration
}

export enum StepType {
  // Input steps
  TEXT_INPUT = 'text_input',
  CSV_IMPORT = 'csv_import',
  URL_FETCH = 'url_fetch',

  // AI processing steps
  AI_GENERATION = 'ai_generation',
  KEYWORD_RESEARCH = 'keyword_research',
  CONTENT_CREATION = 'content_creation',
  SEO_OPTIMIZATION = 'seo_optimization',
  CONTENT_ANALYSIS = 'content_analysis',

  // Human interaction steps
  HUMAN_REVIEW = 'human_review',
  APPROVAL_GATE = 'approval_gate',

  // Output steps
  CMS_PUBLISH = 'cms_publish',
  CMS_INTEGRATION = 'cms_integration',
  CSV_EXPORT = 'csv_export',
  EMAIL_SEND = 'email_send',

  // Logic steps
  CONDITIONAL = 'conditional',
  LOOP = 'loop',
  MERGE = 'merge',
  COMPLETION = 'completion'
}

export interface StepConfig {
  // AI-specific config
  aiConfig?: {
    provider?: string;
    model?: string;
    prompt: string;
    systemPrompt?: string;
    temperature?: number;
    maxTokens?: number;
    userApiKey?: string; // BYOK support
  };

  // Human review config
  reviewConfig?: {
    reviewType: 'approval' | 'editing' | 'feedback';
    reviewers?: string[];
    deadline?: string;
    instructions?: string;
  };

  // CMS config
  cmsConfig?: {
    platform: 'wordpress' | 'shopify' | 'webflow' | 'payload';
    endpoint?: string;
    credentials?: Record<string, string>;
    contentType?: string;
    publishStatus?: 'draft' | 'published';
    autoPublish?: boolean;
    includeMetadata?: boolean;
    publishSettings?: {
      status: 'draft' | 'published';
      categories?: string[];
      tags?: string[];
    };
  };

  // Completion config
  completionConfig?: {
    generateReport?: boolean;
    notifyUser?: boolean;
    archiveArtifacts?: boolean;
    redirectToResults?: boolean;
    resultPageUrl?: string;
  };

  // CSV config
  csvConfig?: {
    delimiter?: string;
    headers?: string[];
    mapping?: Record<string, string>;
  };

  // URL fetch config
  urlConfig?: {
    method?: 'GET' | 'POST';
    headers?: Record<string, string>;
    extractContent?: boolean;
  };

  // SEO optimization config
  seoConfig?: {
    targetKeywords?: string[];
    metaDescriptionLength?: number;
    titleLength?: number;
    enableSchemaMarkup?: boolean;
    enableInternalLinking?: boolean;
    enableImageOptimization?: boolean;
    contentLengthTarget?: number;
    keywordDensityTarget?: number;
    readabilityTarget?: 'basic' | 'intermediate' | 'advanced';
  };

  // Conditional config
  conditionalConfig?: {
    rules: ConditionalRule[];
    defaultAction: 'continue' | 'skip' | 'fail';
  };

  // Loop config
  loopConfig?: {
    iterateOver: string; // Variable name containing array
    maxIterations?: number;
    parallelExecution?: boolean;
  };
}

export interface ConditionalRule {
  variable: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists';
  value: any;
  action: 'continue' | 'skip' | 'fail' | 'branch_to';
  branchTo?: string; // Step ID to branch to
}

export interface StepCondition {
  rules: ConditionalRule[];
  logic: 'AND' | 'OR';
}

// Conditional branching for workflow logic
export interface ConditionalBranch {
  id: string;
  name: string;
  condition: StepCondition;
  steps: string[]; // Step IDs to execute if condition is met
  priority: number; // Higher priority branches are evaluated first
}

// Retry configuration for failed steps
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number; // in milliseconds
  backoffMultiplier?: number; // exponential backoff
  retryConditions?: string[]; // specific error types to retry
}

// Agent Consultation Types
export interface AgentConsultationConfig {
  enabled: boolean;
  triggers: ConsultationTrigger[];
  maxConsultations: number;
  timeoutMs: number;
  fallbackBehavior: 'continue' | 'fail' | 'retry';
  qualityThreshold?: number;
}

export interface ConsultationTrigger {
  type: 'always' | 'quality_threshold' | 'feedback_keywords' | 'content_complexity' | 'user_request';
  condition?: any;
  agents: string[]; // Agent IDs
  priority: 'low' | 'medium' | 'high';
}

export interface AgentRequirement {
  agentId: string;
  expertise: string[];
  required: boolean;
  weight: number;
}

export interface WorkflowMetadata {
  category: 'blog' | 'ecommerce' | 'social' | 'email' | 'seo' | 'custom';
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  author?: string;
  featured?: boolean;
}

// Workflow Execution Types
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  stepResults: Record<string, StepResult>;
  currentStep?: string;
  progress: number;
  startedAt: string;
  completedAt?: string;
  error?: ExecutionError;
  metadata: ExecutionMetadata;
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PAUSED = 'paused',
  WAITING_REVIEW = 'waiting_review',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface StepResult {
  stepId: string;
  status: StepStatus;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  error?: string;
  metadata?: Record<string, any>;
  artifactId?: string; // ID of artifact created by this step
  approvalRequired?: boolean; // Whether this step requires approval
  approvedBy?: string; // Who approved this step
  approvedAt?: string; // When this step was approved
  rejectionReason?: string; // Reason for rejection if rejected
  stepType?: StepType; // Type of step for dependency checking
}

export enum StepStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
  WAITING_REVIEW = 'waiting_review',
  WAITING_APPROVAL = 'waiting_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface ExecutionError {
  stepId?: string;
  message: string;
  code?: string;
  recoverable: boolean;
  timestamp: string;
  stack?: string;
}

export interface ExecutionMetadata {
  userId?: string;
  sessionId?: string;
  source: 'api' | 'ui' | 'template';
  priority: 'low' | 'normal' | 'high';
  tags?: string[];
}

// Workflow Engine Interface
export interface IWorkflowEngine {
  // Workflow management
  createWorkflow(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  getWorkflow(id: string): Promise<Workflow | null>;
  updateWorkflow(id: string, updates: Partial<Workflow>): Promise<void>;
  deleteWorkflow(id: string): Promise<void>;
  listWorkflows(filters?: WorkflowFilters): Promise<Workflow[]>;

  // Execution management
  executeWorkflow(workflowId: string, inputs: Record<string, any>, metadata?: Partial<ExecutionMetadata>): Promise<string>;
  getExecution(id: string): Promise<WorkflowExecution | null>;
  pauseExecution(id: string): Promise<void>;
  resumeExecution(id: string): Promise<void>;
  cancelExecution(id: string): Promise<void>;

  // Step execution
  executeStep(executionId: string, stepId: string): Promise<StepResult>;
  retryStep(executionId: string, stepId: string): Promise<StepResult>;

  // Review handling
  submitReview(executionId: string, stepId: string, decision: ReviewDecision): Promise<void>;

  // Artifact management
  createArtifact(executionId: string, stepId: string, artifact: Omit<WorkflowArtifact, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  getArtifact(artifactId: string): Promise<WorkflowArtifact | null>;
  updateArtifact(artifactId: string, updates: Partial<WorkflowArtifact>): Promise<void>;

  // Approval handling
  submitApproval(artifactId: string, decision: ApprovalDecision): Promise<void>;
  getApprovalStatus(artifactId: string): Promise<ApprovalStatus>;
}

export interface WorkflowFilters {
  category?: string;
  tags?: string[];
  difficulty?: string;
  featured?: boolean;
  author?: string;
}

export interface ReviewDecision {
  approved: boolean;
  feedback?: string;
  edits?: Record<string, any>;
  reviewer: string;
}

// Artifact Management Types
export interface WorkflowArtifact {
  id: string;
  stepId: string;
  executionId: string;
  type: ArtifactType;
  title: string;
  content: any;
  status: ArtifactStatus;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  metadata?: Record<string, any>;
}

export enum ArtifactType {
  KEYWORD_RESEARCH = 'keyword_research',
  CONTENT_STRATEGY = 'content_strategy',
  CONTENT_DRAFT = 'content_draft',
  SEO_OPTIMIZATION = 'seo_optimization',
  FINAL_CONTENT = 'final_content',
  REVIEW_FEEDBACK = 'review_feedback'
}

export enum ArtifactStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REVISION_REQUIRED = 'revision_required'
}

// Approval Gate Configuration
export interface ApprovalGate {
  id: string;
  stepId: string;
  artifactType: ArtifactType;
  approvers: string[]; // List of user IDs who can approve
  requiredApprovals: number; // Number of approvals needed
  autoApprove?: boolean; // Auto-approve based on criteria
  approvalCriteria?: ApprovalCriteria;
  timeout?: number; // Timeout in hours
  escalation?: EscalationConfig;
}

export interface ApprovalCriteria {
  qualityScore?: number; // Minimum quality score
  keywordDensity?: number; // For SEO content
  wordCount?: { min?: number; max?: number };
  customRules?: CustomApprovalRule[];
}

export interface CustomApprovalRule {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
  required: boolean;
}

export interface EscalationConfig {
  enabled: boolean;
  escalateAfterHours: number;
  escalateTo: string[]; // User IDs to escalate to
  maxEscalations: number;
}

// Approval Decision Types
export interface ApprovalDecision {
  approved: boolean;
  approver: string;
  feedback?: string;
  timestamp: string;
  reason?: string;
}

export interface ApprovalStatus {
  artifactId: string;
  status: ArtifactStatus;
  approvals: ApprovalDecision[];
  requiredApprovals: number;
  pendingApprovers: string[];
  canProceed: boolean;
  escalated: boolean;
  escalationLevel: number;
}

// Template Types
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>;
  sampleInputs: Record<string, any>;
  instructions: string;
  featured: boolean;
}

// Variable Context for Step Execution
export interface VariableContext {
  [key: string]: any;
}

// Step Executor Interface
export interface IStepExecutor {
  canExecute(stepType: StepType): boolean;
  execute(step: WorkflowStep, context: VariableContext): Promise<Record<string, any>>;
}

// Workflow Events
export interface WorkflowEvent {
  id: string;
  type: WorkflowEventType;
  executionId: string;
  stepId?: string;
  timestamp: string;
  data: any;
}

export enum WorkflowEventType {
  EXECUTION_STARTED = 'execution_started',
  EXECUTION_COMPLETED = 'execution_completed',
  EXECUTION_FAILED = 'execution_failed',
  EXECUTION_PAUSED = 'execution_paused',
  EXECUTION_RESUMED = 'execution_resumed',
  STEP_STARTED = 'step_started',
  STEP_COMPLETED = 'step_completed',
  STEP_FAILED = 'step_failed',
  REVIEW_REQUESTED = 'review_requested',
  REVIEW_COMPLETED = 'review_completed'
}
