/**
 * Real-time Progress Component
 * Displays live workflow progress with step-by-step updates
 */

'use client';

import React from 'react';
import { useWorkflowProgress } from '../../hooks/useWorkflowProgress';

interface RealTimeProgressProps {
  executionId?: string;
  showSteps?: boolean;
  showEvents?: boolean;
  compact?: boolean;
  className?: string;
}

export function RealTimeProgress({
  executionId,
  showSteps = true,
  showEvents = false,
  compact = false,
  className = ''
}: RealTimeProgressProps) {
  const { state, isConnected, connectionError } = useWorkflowProgress({
    executionId,
    autoConnect: true,
    reconnectAttempts: 5
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600 bg-blue-50';
      case 'completed': return 'text-green-600 bg-green-50';
      case 'failed': return 'text-red-600 bg-red-50';
      case 'paused': return 'text-yellow-600 bg-yellow-50';
      case 'waiting_review': return 'text-purple-600 bg-purple-50';
      case 'waiting_approval': return 'text-orange-600 bg-orange-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return '🔄';
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'paused': return '⏸️';
      case 'waiting_review': return '👥';
      case 'waiting_approval': return '⏳';
      case 'pending': return '⭕';
      default: return '⚪';
    }
  };

  const getConnectionStatusIndicator = () => {
    switch (state.connectionStatus) {
      case 'connected':
        return <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Connected" />;
      case 'connecting':
        return <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" title="Connecting..." />;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full" title="Connection Error" />;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full" title="Disconnected" />;
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {getConnectionStatusIndicator()}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{getStatusIcon(state.status)}</span>
          <span className="text-sm text-gray-600 capitalize">{state.status}</span>
        </div>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${state.progress}%` }}
          />
        </div>
        <span className="text-sm font-medium text-gray-700">{state.progress}%</span>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <h3 className="text-lg font-semibold text-gray-900">Workflow Progress</h3>
          {getConnectionStatusIndicator()}
          {!isConnected && connectionError && (
            <span className="text-sm text-red-600">{connectionError}</span>
          )}
        </div>
        {state.executionId && (
          <span className="text-sm text-gray-500 font-mono">
            {state.executionId.slice(0, 8)}...
          </span>
        )}
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getStatusIcon(state.status)}</span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColor(state.status)}`}>
              {state.status}
            </span>
          </div>
          <span className="text-lg font-bold text-gray-900">{state.progress}%</span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${
              state.status === 'failed' ? 'bg-red-500' :
              state.status === 'completed' ? 'bg-green-500' :
              state.status === 'paused' ? 'bg-yellow-500' :
              'bg-blue-500'
            }`}
            style={{ width: `${state.progress}%` }}
          />
        </div>

        {state.currentStep && (
          <p className="text-sm text-gray-600 mt-2">
            Current step: <span className="font-medium">{state.currentStep}</span>
          </p>
        )}

        {state.error && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">{state.error}</p>
          </div>
        )}
      </div>

      {/* Step Progress */}
      {showSteps && state.stepResults.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-3">Steps</h4>
          <div className="space-y-3">
            {state.stepResults.map((step, index) => (
              <div key={step.stepId} className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getStatusIcon(step.status)}</span>
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {step.stepName || step.stepId}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${getStatusColor(step.status)}`}>
                      {step.status.replace('_', ' ')}
                    </span>
                  </div>
                  {step.duration && (
                    <p className="text-xs text-gray-500 mt-1">
                      Duration: {formatDuration(step.duration)}
                    </p>
                  )}
                  {step.error && (
                    <p className="text-xs text-red-600 mt-1">{step.error}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Events */}
      {showEvents && state.events.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Recent Events</h4>
          <div className="bg-gray-50 rounded-md p-3 max-h-48 overflow-y-auto">
            <div className="space-y-2">
              {state.events.slice(-10).reverse().map((event) => (
                <div key={event.id} className="text-xs">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-500">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </span>
                    <span className="font-medium text-gray-700">{event.type}</span>
                  </div>
                  {event.data && Object.keys(event.data).length > 0 && (
                    <pre className="text-gray-600 mt-1 whitespace-pre-wrap">
                      {JSON.stringify(event.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Last Update */}
      {state.lastUpdate && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Last updated: {new Date(state.lastUpdate).toLocaleString()}
          </p>
        </div>
      )}
    </div>
  );
}

export default RealTimeProgress;
