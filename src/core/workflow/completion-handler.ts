/**
 * Workflow Completion Handler
 * Handles final workflow completion, cleanup, and notifications
 */

import { WorkflowExecution, ExecutionStatus } from './types';

export interface CompletionReport {
  executionId: string;
  workflowId: string;
  completedAt: string;
  duration: number;
  status: 'completed' | 'failed' | 'cancelled';
  summary: CompletionSummary;
  outputs: CompletionOutputs;
  metrics: CompletionMetrics;
  notifications: NotificationResult[];
  archiveInfo?: ArchiveInfo;
}

export interface CompletionSummary {
  stepsCompleted: number;
  totalSteps: number;
  successRate: number;
  agentConsultations: number;
  humanInterventions: number;
  regenerationCount: number;
  finalContent: boolean;
  cmsPublished: boolean;
  cmsPostId?: string;
  qualityScore?: number;
  seoScore?: number;
}

export interface CompletionOutputs {
  finalContent?: string;
  resultsPackage?: any;
  cmsDetails?: {
    postId: string;
    url: string;
    status: string;
    collection?: string;
  };
  exportUrls?: Record<string, string>;
  downloadLinks?: string[];
}

export interface CompletionMetrics {
  executionTime: number;
  averageStepTime: number;
  processingEfficiency: number;
  contentQuality: number | string;
  wordCount: number | string;
  seoScore: number | string;
  userSatisfaction?: number;
  costMetrics?: {
    totalCost: number;
    aiCost: number;
    humanCost: number;
  };
}

export interface NotificationResult {
  type: 'email' | 'webhook' | 'internal';
  recipient: string;
  status: 'sent' | 'failed' | 'pending';
  timestamp: string;
  message?: string;
  error?: string;
}

export interface ArchiveInfo {
  archiveId: string;
  archiveUrl: string;
  archivedAt: string;
  retentionPeriod: string;
  size: number;
}

export interface CompletionConfig {
  generateReport?: boolean;
  notifyUser?: boolean;
  archiveArtifacts?: boolean;
  redirectToResults?: boolean;
  resultPageUrl?: string;
  enableWebhooks?: boolean;
  webhookUrls?: string[];
  emailNotifications?: boolean;
  emailRecipients?: string[];
  cleanupTempFiles?: boolean;
  retentionPeriod?: string;
}

export class WorkflowCompletionHandler {
  /**
   * Handle workflow completion
   */
  async handleCompletion(
    execution: WorkflowExecution,
    config: CompletionConfig = {},
    inputs: Record<string, any> = {}
  ): Promise<CompletionReport> {
    console.log(`🎉 Handling workflow completion for execution ${execution.id}`);

    const startTime = Date.now();
    const completedAt = new Date().toISOString();
    const duration = Date.now() - new Date(execution.startedAt).getTime();

    // Generate completion summary
    const summary = this.generateCompletionSummary(execution, inputs);

    // Prepare completion outputs
    const outputs = this.prepareCompletionOutputs(execution, inputs);

    // Calculate completion metrics
    const metrics = this.calculateCompletionMetrics(execution, inputs, duration);

    // Handle notifications
    const notifications = await this.handleNotifications(execution, config, summary);

    // Archive artifacts if requested
    let archiveInfo: ArchiveInfo | undefined;
    if (config.archiveArtifacts) {
      archiveInfo = await this.archiveArtifacts(execution, config);
    }

    // Cleanup temporary files if requested
    if (config.cleanupTempFiles) {
      await this.cleanupTempFiles(execution);
    }

    const completionReport: CompletionReport = {
      executionId: execution.id,
      workflowId: execution.workflowId,
      completedAt,
      duration,
      status: this.determineCompletionStatus(execution),
      summary,
      outputs,
      metrics,
      notifications,
      archiveInfo
    };

    console.log(`✅ Workflow completion handled for execution ${execution.id}`);
    console.log(`📊 Completion summary: ${summary.stepsCompleted}/${summary.totalSteps} steps completed`);

    return completionReport;
  }

  /**
   * Generate completion summary
   */
  private generateCompletionSummary(execution: WorkflowExecution, inputs: Record<string, any>): CompletionSummary {
    const stepResults = Object.values(execution.stepResults);
    const totalSteps = stepResults.length;
    const completedSteps = stepResults.filter(step => step.status === 'completed' || step.status === 'approved').length;
    const successRate = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

    // Count different types of steps
    const agentConsultations = stepResults.filter(step => 
      step.stepType === 'ai_generation' && step.outputs?.consultationResults
    ).length;

    const humanInterventions = stepResults.filter(step => 
      step.stepType === 'human_review' || step.stepType === 'approval_gate'
    ).length;

    // Extract key information from inputs
    const cmsPostId = inputs.cms_post_id;
    const resultsPackage = inputs.results_package || {};
    const finalContent = inputs.final_content;

    return {
      stepsCompleted: completedSteps,
      totalSteps,
      successRate,
      agentConsultations,
      humanInterventions,
      regenerationCount: 0, // TODO: Track regenerations
      finalContent: !!finalContent,
      cmsPublished: !!cmsPostId,
      cmsPostId,
      qualityScore: resultsPackage.qualityMetrics?.overallQualityScore,
      seoScore: resultsPackage.seoMetrics?.overallSEOScore
    };
  }

  /**
   * Prepare completion outputs
   */
  private prepareCompletionOutputs(execution: WorkflowExecution, inputs: Record<string, any>): CompletionOutputs {
    const outputs: CompletionOutputs = {};

    // Final content
    if (inputs.final_content) {
      outputs.finalContent = inputs.final_content;
    }

    // Results package
    if (inputs.results_package) {
      outputs.resultsPackage = inputs.results_package;
    }

    // CMS details
    if (inputs.cms_post_id) {
      outputs.cmsDetails = {
        postId: inputs.cms_post_id,
        url: inputs.cms_url || `/blog/${inputs.cms_post_id}`,
        status: inputs.publish_status || 'published',
        collection: inputs.collection || 'posts'
      };
    }

    // Export URLs
    if (inputs.results_package?.exportMetadata?.downloadUrls) {
      outputs.exportUrls = inputs.results_package.exportMetadata.downloadUrls;
      outputs.downloadLinks = Object.values(inputs.results_package.exportMetadata.downloadUrls);
    }

    return outputs;
  }

  /**
   * Calculate completion metrics
   */
  private calculateCompletionMetrics(
    execution: WorkflowExecution, 
    inputs: Record<string, any>, 
    duration: number
  ): CompletionMetrics {
    const stepResults = Object.values(execution.stepResults);
    const stepDurations = stepResults.map(step => step.duration || 0);
    const averageStepTime = stepDurations.length > 0 
      ? stepDurations.reduce((sum, d) => sum + d, 0) / stepDurations.length 
      : 0;

    const aiSteps = stepResults.filter(step => step.stepType === 'ai_generation');
    const aiTime = aiSteps.reduce((sum, step) => sum + (step.duration || 0), 0);
    const processingEfficiency = duration > 0 ? (aiTime / duration) * 100 : 0;

    const resultsPackage = inputs.results_package || {};

    return {
      executionTime: duration,
      averageStepTime,
      processingEfficiency,
      contentQuality: resultsPackage.qualityMetrics?.overallQualityScore || 'N/A',
      wordCount: resultsPackage.contentSummary?.wordCount || 'N/A',
      seoScore: resultsPackage.seoMetrics?.overallSEOScore || 'N/A',
      userSatisfaction: 85, // Default satisfaction score
      costMetrics: {
        totalCost: 0, // TODO: Calculate actual costs
        aiCost: 0,
        humanCost: 0
      }
    };
  }

  /**
   * Handle notifications
   */
  private async handleNotifications(
    execution: WorkflowExecution,
    config: CompletionConfig,
    summary: CompletionSummary
  ): Promise<NotificationResult[]> {
    const notifications: NotificationResult[] = [];

    // Email notifications
    if (config.emailNotifications && config.emailRecipients) {
      for (const recipient of config.emailRecipients) {
        try {
          await this.sendEmailNotification(execution, recipient, summary);
          notifications.push({
            type: 'email',
            recipient,
            status: 'sent',
            timestamp: new Date().toISOString(),
            message: 'Workflow completion notification sent'
          });
        } catch (error) {
          notifications.push({
            type: 'email',
            recipient,
            status: 'failed',
            timestamp: new Date().toISOString(),
            error: error.message
          });
        }
      }
    }

    // Webhook notifications
    if (config.enableWebhooks && config.webhookUrls) {
      for (const webhookUrl of config.webhookUrls) {
        try {
          await this.sendWebhookNotification(execution, webhookUrl, summary);
          notifications.push({
            type: 'webhook',
            recipient: webhookUrl,
            status: 'sent',
            timestamp: new Date().toISOString(),
            message: 'Webhook notification sent'
          });
        } catch (error) {
          notifications.push({
            type: 'webhook',
            recipient: webhookUrl,
            status: 'failed',
            timestamp: new Date().toISOString(),
            error: error.message
          });
        }
      }
    }

    // Internal notifications (always sent)
    notifications.push({
      type: 'internal',
      recipient: 'system',
      status: 'sent',
      timestamp: new Date().toISOString(),
      message: `Workflow ${execution.id} completed successfully`
    });

    return notifications;
  }

  /**
   * Archive artifacts
   */
  private async archiveArtifacts(execution: WorkflowExecution, config: CompletionConfig): Promise<ArchiveInfo> {
    console.log(`📦 Archiving artifacts for execution ${execution.id}`);

    // This is a simplified implementation
    // In a real system, you'd compress and store artifacts
    const archiveId = `archive-${execution.id}-${Date.now()}`;
    const archiveUrl = `/api/workflow/archive/${archiveId}`;
    const retentionPeriod = config.retentionPeriod || '1 year';

    return {
      archiveId,
      archiveUrl,
      archivedAt: new Date().toISOString(),
      retentionPeriod,
      size: 0 // TODO: Calculate actual archive size
    };
  }

  /**
   * Cleanup temporary files
   */
  private async cleanupTempFiles(execution: WorkflowExecution): Promise<void> {
    console.log(`🧹 Cleaning up temporary files for execution ${execution.id}`);
    // Implementation would clean up any temporary files created during execution
  }

  /**
   * Determine completion status
   */
  private determineCompletionStatus(execution: WorkflowExecution): 'completed' | 'failed' | 'cancelled' {
    if (execution.status === ExecutionStatus.COMPLETED) {
      return 'completed';
    } else if (execution.status === ExecutionStatus.FAILED) {
      return 'failed';
    } else if (execution.status === ExecutionStatus.CANCELLED) {
      return 'cancelled';
    } else {
      return 'completed'; // Default to completed
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(
    execution: WorkflowExecution,
    recipient: string,
    summary: CompletionSummary
  ): Promise<void> {
    console.log(`📧 Sending email notification to ${recipient} for execution ${execution.id}`);
    // Implementation would send actual email
    // For now, just log the notification
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(
    execution: WorkflowExecution,
    webhookUrl: string,
    summary: CompletionSummary
  ): Promise<void> {
    console.log(`🔗 Sending webhook notification to ${webhookUrl} for execution ${execution.id}`);
    
    const payload = {
      event: 'workflow.completed',
      executionId: execution.id,
      workflowId: execution.workflowId,
      timestamp: new Date().toISOString(),
      summary
    };

    // Implementation would send actual HTTP request
    // For now, just log the webhook
    console.log(`🔗 Webhook payload:`, payload);
  }
}
