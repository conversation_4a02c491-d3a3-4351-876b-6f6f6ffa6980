/**
 * Payload CMS Integration
 * Handles content publishing to Payload CMS with full metadata support
 */

import { getPayload } from 'payload';
import config from '@payload-config';

export interface PayloadPublishRequest {
  title: string;
  content: string;
  status: 'draft' | 'published';
  meta?: {
    description?: string;
    keywords?: string;
    image?: string;
  };
  categories?: string[];
  tags?: string[];
  author?: string;
  publishedDate?: string;
  workflow?: {
    executionId: string;
    templateId?: string;
    generatedAt: string;
    agentConsultations?: any;
  };
  seoMetadata?: {
    title?: string;
    description?: string;
    keywords?: string[];
    schemaMarkup?: Record<string, any>;
  };
}

export interface PayloadPublishResponse {
  id: string;
  url: string;
  status: 'draft' | 'published';
  publishedAt?: string;
  collection: string;
  slug?: string;
}

export interface MediaUploadRequest {
  filename: string;
  mimeType: string;
  buffer: Buffer;
  alt?: string;
  description?: string;
}

export interface MediaUploadResponse {
  id: string;
  url: string;
  filename: string;
  mimeType: string;
  filesize: number;
  width?: number;
  height?: number;
}

export class PayloadIntegration {
  private payload: any;

  constructor() {
    this.payload = null;
  }

  /**
   * Initialize Payload instance
   */
  private async getPayloadInstance() {
    if (!this.payload) {
      this.payload = await getPayload({ config });
    }
    return this.payload;
  }

  /**
   * Publish content to Payload CMS
   */
  async publishContent(request: PayloadPublishRequest): Promise<PayloadPublishResponse> {
    console.log(`📝 Publishing content to Payload CMS: ${request.title}`);

    try {
      const payload = await this.getPayloadInstance();

      // Determine the collection to use (you may need to adjust based on your collections)
      const collection = 'posts'; // Assuming you have a posts collection

      // Prepare the document data
      const documentData = {
        title: request.title,
        content: request.content,
        status: request.status,
        publishedDate: request.publishedDate || (request.status === 'published' ? new Date().toISOString() : undefined),
        author: request.author || 'workflow-system',
        
        // Meta information
        meta: {
          description: request.meta?.description || request.seoMetadata?.description || '',
          keywords: request.meta?.keywords || request.seoMetadata?.keywords?.join(', ') || '',
          image: request.meta?.image
        },

        // Categories and tags
        categories: request.categories || [],
        tags: request.tags || [],

        // SEO metadata
        seo: {
          title: request.seoMetadata?.title || request.title,
          description: request.seoMetadata?.description || request.meta?.description || '',
          keywords: request.seoMetadata?.keywords || [],
          schemaMarkup: request.seoMetadata?.schemaMarkup
        },

        // Workflow metadata
        workflow: request.workflow ? {
          executionId: request.workflow.executionId,
          templateId: request.workflow.templateId,
          generatedAt: request.workflow.generatedAt,
          agentConsultations: request.workflow.agentConsultations
        } : undefined
      };

      console.log(`📝 Creating document in collection '${collection}'...`);

      // Create the document
      const result = await payload.create({
        collection,
        data: documentData
      });

      console.log(`✅ Content published successfully with ID: ${result.id}`);

      // Generate URL (adjust based on your URL structure)
      const url = `/blog/${result.slug || result.id}`;

      return {
        id: result.id,
        url,
        status: result.status,
        publishedAt: result.publishedDate,
        collection,
        slug: result.slug
      };

    } catch (error) {
      console.error(`❌ Failed to publish content to Payload CMS:`, error);
      throw new Error(`Payload CMS publishing failed: ${error.message}`);
    }
  }

  /**
   * Update existing content
   */
  async updateContent(id: string, updates: Partial<PayloadPublishRequest>): Promise<PayloadPublishResponse> {
    console.log(`📝 Updating content in Payload CMS: ${id}`);

    try {
      const payload = await this.getPayloadInstance();
      const collection = 'posts';

      // Prepare update data
      const updateData: any = {};
      
      if (updates.title) updateData.title = updates.title;
      if (updates.content) updateData.content = updates.content;
      if (updates.status) updateData.status = updates.status;
      if (updates.categories) updateData.categories = updates.categories;
      if (updates.tags) updateData.tags = updates.tags;
      
      if (updates.meta || updates.seoMetadata) {
        updateData.meta = {
          description: updates.meta?.description || updates.seoMetadata?.description,
          keywords: updates.meta?.keywords || updates.seoMetadata?.keywords?.join(', '),
          image: updates.meta?.image
        };
      }

      if (updates.seoMetadata) {
        updateData.seo = {
          title: updates.seoMetadata.title,
          description: updates.seoMetadata.description,
          keywords: updates.seoMetadata.keywords,
          schemaMarkup: updates.seoMetadata.schemaMarkup
        };
      }

      const result = await payload.update({
        collection,
        id,
        data: updateData
      });

      console.log(`✅ Content updated successfully: ${id}`);

      return {
        id: result.id,
        url: `/blog/${result.slug || result.id}`,
        status: result.status,
        publishedAt: result.publishedDate,
        collection,
        slug: result.slug
      };

    } catch (error) {
      console.error(`❌ Failed to update content in Payload CMS:`, error);
      throw new Error(`Payload CMS update failed: ${error.message}`);
    }
  }

  /**
   * Upload media to Payload CMS
   */
  async uploadMedia(request: MediaUploadRequest): Promise<MediaUploadResponse> {
    console.log(`📷 Uploading media to Payload CMS: ${request.filename}`);

    try {
      const payload = await this.getPayloadInstance();
      const collection = 'media';

      const result = await payload.create({
        collection,
        data: {
          alt: request.alt || request.filename,
          description: request.description || ''
        },
        file: {
          data: request.buffer,
          mimetype: request.mimeType,
          name: request.filename,
          size: request.buffer.length
        }
      });

      console.log(`✅ Media uploaded successfully with ID: ${result.id}`);

      return {
        id: result.id,
        url: result.url,
        filename: result.filename,
        mimeType: result.mimeType,
        filesize: result.filesize,
        width: result.width,
        height: result.height
      };

    } catch (error) {
      console.error(`❌ Failed to upload media to Payload CMS:`, error);
      throw new Error(`Payload CMS media upload failed: ${error.message}`);
    }
  }

  /**
   * Get content by ID
   */
  async getContent(id: string): Promise<any> {
    try {
      const payload = await this.getPayloadInstance();
      const collection = 'posts';

      const result = await payload.findByID({
        collection,
        id
      });

      return result;

    } catch (error) {
      console.error(`❌ Failed to get content from Payload CMS:`, error);
      throw new Error(`Payload CMS get content failed: ${error.message}`);
    }
  }

  /**
   * Delete content by ID
   */
  async deleteContent(id: string): Promise<void> {
    try {
      const payload = await this.getPayloadInstance();
      const collection = 'posts';

      await payload.delete({
        collection,
        id
      });

      console.log(`✅ Content deleted successfully: ${id}`);

    } catch (error) {
      console.error(`❌ Failed to delete content from Payload CMS:`, error);
      throw new Error(`Payload CMS delete content failed: ${error.message}`);
    }
  }

  /**
   * Search content
   */
  async searchContent(query: string, limit: number = 10): Promise<any[]> {
    try {
      const payload = await this.getPayloadInstance();
      const collection = 'posts';

      const result = await payload.find({
        collection,
        where: {
          or: [
            {
              title: {
                contains: query
              }
            },
            {
              content: {
                contains: query
              }
            }
          ]
        },
        limit
      });

      return result.docs;

    } catch (error) {
      console.error(`❌ Failed to search content in Payload CMS:`, error);
      throw new Error(`Payload CMS search failed: ${error.message}`);
    }
  }

  /**
   * Get content statistics
   */
  async getContentStats(): Promise<{
    totalPosts: number;
    publishedPosts: number;
    draftPosts: number;
    totalMedia: number;
  }> {
    try {
      const payload = await this.getPayloadInstance();

      const [postsResult, mediaResult] = await Promise.all([
        payload.find({
          collection: 'posts',
          limit: 0 // Just get count
        }),
        payload.find({
          collection: 'media',
          limit: 0 // Just get count
        })
      ]);

      const publishedResult = await payload.find({
        collection: 'posts',
        where: {
          status: {
            equals: 'published'
          }
        },
        limit: 0
      });

      const draftResult = await payload.find({
        collection: 'posts',
        where: {
          status: {
            equals: 'draft'
          }
        },
        limit: 0
      });

      return {
        totalPosts: postsResult.totalDocs,
        publishedPosts: publishedResult.totalDocs,
        draftPosts: draftResult.totalDocs,
        totalMedia: mediaResult.totalDocs
      };

    } catch (error) {
      console.error(`❌ Failed to get content stats from Payload CMS:`, error);
      throw new Error(`Payload CMS stats failed: ${error.message}`);
    }
  }

  /**
   * Validate CMS connection
   */
  async validateConnection(): Promise<boolean> {
    try {
      const payload = await this.getPayloadInstance();
      
      // Try to get collections to validate connection
      const collections = payload.config.collections;
      
      console.log(`✅ Payload CMS connection validated. Available collections: ${collections.map((c: any) => c.slug).join(', ')}`);
      
      return true;

    } catch (error) {
      console.error(`❌ Payload CMS connection validation failed:`, error);
      return false;
    }
  }
}
