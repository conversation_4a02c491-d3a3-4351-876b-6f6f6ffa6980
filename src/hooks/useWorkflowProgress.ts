/**
 * useWorkflowProgress Hook
 * React hook for real-time workflow progress tracking via SSE
 */

import { useState, useEffect, useRef, useCallback } from 'react';

export interface WorkflowProgressState {
  executionId?: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'failed';
  progress: number;
  currentStep?: string;
  stepResults: StepProgressInfo[];
  error?: string;
  lastUpdate?: string;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  events: WorkflowEvent[];
}

export interface StepProgressInfo {
  stepId: string;
  stepName?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'waiting_review' | 'waiting_approval';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  error?: string;
}

export interface WorkflowEvent {
  type: string;
  data: any;
  timestamp: string;
  id: string;
}

export interface UseWorkflowProgressOptions {
  executionId?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  maxEvents?: number;
}

export interface UseWorkflowProgressReturn {
  state: WorkflowProgressState;
  connect: () => void;
  disconnect: () => void;
  isConnected: boolean;
  lastEvent?: WorkflowEvent;
  connectionError?: string;
}

export function useWorkflowProgress(options: UseWorkflowProgressOptions = {}): UseWorkflowProgressReturn {
  const {
    executionId,
    autoConnect = true,
    reconnectAttempts = 3,
    reconnectDelay = 2000,
    maxEvents = 50
  } = options;

  const [state, setState] = useState<WorkflowProgressState>({
    status: 'idle',
    progress: 0,
    stepResults: [],
    connectionStatus: 'disconnected',
    events: []
  });

  const [connectionError, setConnectionError] = useState<string>();
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const clientIdRef = useRef<string>(`client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

  // Generate event ID
  const generateEventId = useCallback(() => {
    return `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Add event to state
  const addEvent = useCallback((type: string, data: any) => {
    const event: WorkflowEvent = {
      type,
      data,
      timestamp: new Date().toISOString(),
      id: generateEventId()
    };

    setState(prevState => ({
      ...prevState,
      events: [...prevState.events.slice(-(maxEvents - 1)), event],
      lastUpdate: event.timestamp
    }));

    return event;
  }, [maxEvents, generateEventId]);

  // Process workflow event
  const processEvent = useCallback((eventType: string, eventData: any) => {
    console.log(`📡 Processing workflow event: ${eventType}`, eventData);

    const event = addEvent(eventType, eventData);

    setState(prevState => {
      const newState = { ...prevState };

      switch (eventType) {
        case 'connection':
          newState.connectionStatus = 'connected';
          setConnectionError(undefined);
          reconnectCountRef.current = 0;
          break;

        case 'execution_status':
          newState.executionId = eventData.executionId;
          newState.status = eventData.status;
          newState.progress = eventData.progress || 0;
          newState.currentStep = eventData.currentStep;
          if (eventData.stepResults) {
            newState.stepResults = eventData.stepResults.map((step: any) => ({
              stepId: step.stepId,
              stepName: step.stepName,
              status: step.status,
              startedAt: step.startedAt,
              completedAt: step.completedAt,
              duration: step.completedAt && step.startedAt 
                ? new Date(step.completedAt).getTime() - new Date(step.startedAt).getTime()
                : undefined
            }));
          }
          break;

        case 'workflow_started':
          newState.executionId = eventData.executionId;
          newState.status = 'running';
          newState.progress = 0;
          newState.error = undefined;
          break;

        case 'workflow_completed':
          newState.status = 'completed';
          newState.progress = 100;
          newState.currentStep = undefined;
          break;

        case 'workflow_failed':
          newState.status = 'failed';
          newState.error = eventData.error?.message || 'Workflow failed';
          break;

        case 'workflow_paused':
          newState.status = 'paused';
          break;

        case 'step_started':
          newState.currentStep = eventData.stepId;
          newState.stepResults = newState.stepResults.map(step => 
            step.stepId === eventData.stepId 
              ? { ...step, status: 'running', startedAt: eventData.startedAt }
              : step
          );
          // Add step if not exists
          if (!newState.stepResults.find(step => step.stepId === eventData.stepId)) {
            newState.stepResults.push({
              stepId: eventData.stepId,
              stepName: eventData.stepName,
              status: 'running',
              startedAt: eventData.startedAt
            });
          }
          break;

        case 'step_completed':
          newState.stepResults = newState.stepResults.map(step => 
            step.stepId === eventData.stepId 
              ? { 
                  ...step, 
                  status: 'completed', 
                  completedAt: eventData.completedAt,
                  duration: step.startedAt 
                    ? new Date(eventData.completedAt).getTime() - new Date(step.startedAt).getTime()
                    : undefined
                }
              : step
          );
          break;

        case 'step_failed':
          newState.stepResults = newState.stepResults.map(step => 
            step.stepId === eventData.stepId 
              ? { 
                  ...step, 
                  status: 'failed', 
                  error: eventData.error?.message,
                  completedAt: eventData.failedAt
                }
              : step
          );
          break;

        case 'progress_update':
          newState.progress = eventData.progress;
          newState.currentStep = eventData.currentStep;
          break;

        case 'review_requested':
          newState.stepResults = newState.stepResults.map(step => 
            step.stepId === eventData.stepId 
              ? { ...step, status: 'waiting_review' }
              : step
          );
          break;

        case 'review_completed':
          newState.stepResults = newState.stepResults.map(step => 
            step.stepId === eventData.stepId 
              ? { ...step, status: eventData.decision === 'approved' ? 'completed' : 'running' }
              : step
          );
          break;

        case 'heartbeat':
          // Update connection status
          newState.connectionStatus = 'connected';
          break;
      }

      return newState;
    });
  }, [addEvent]);

  // Connect to SSE
  const connect = useCallback(() => {
    if (eventSourceRef.current) {
      return; // Already connected
    }

    setState(prevState => ({ ...prevState, connectionStatus: 'connecting' }));
    setConnectionError(undefined);

    const url = new URL('/api/workflow/events', window.location.origin);
    if (executionId) {
      url.searchParams.set('executionId', executionId);
    }
    url.searchParams.set('clientId', clientIdRef.current);

    console.log(`📡 Connecting to SSE: ${url.toString()}`);

    const eventSource = new EventSource(url.toString());
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      console.log('📡 SSE connection opened');
      setState(prevState => ({ ...prevState, connectionStatus: 'connected' }));
      setConnectionError(undefined);
      reconnectCountRef.current = 0;
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        processEvent(data.type, data.data);
      } catch (error) {
        console.error('❌ Error parsing SSE message:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('❌ SSE connection error:', error);
      setState(prevState => ({ ...prevState, connectionStatus: 'error' }));
      
      eventSource.close();
      eventSourceRef.current = null;

      // Attempt reconnection
      if (reconnectCountRef.current < reconnectAttempts) {
        reconnectCountRef.current++;
        const delay = reconnectDelay * Math.pow(2, reconnectCountRef.current - 1); // Exponential backoff
        
        console.log(`🔄 Attempting reconnection ${reconnectCountRef.current}/${reconnectAttempts} in ${delay}ms`);
        setConnectionError(`Connection lost. Reconnecting... (${reconnectCountRef.current}/${reconnectAttempts})`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, delay);
      } else {
        setConnectionError('Connection failed. Please refresh the page.');
        setState(prevState => ({ ...prevState, connectionStatus: 'disconnected' }));
      }
    };
  }, [executionId, reconnectAttempts, reconnectDelay, processEvent]);

  // Disconnect from SSE
  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      console.log('📡 Disconnecting from SSE');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setState(prevState => ({ ...prevState, connectionStatus: 'disconnected' }));
    reconnectCountRef.current = 0;
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Reconnect when executionId changes
  useEffect(() => {
    if (eventSourceRef.current && executionId) {
      disconnect();
      setTimeout(() => connect(), 100);
    }
  }, [executionId, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    state,
    connect,
    disconnect,
    isConnected: state.connectionStatus === 'connected',
    lastEvent: state.events[state.events.length - 1],
    connectionError
  };
}
