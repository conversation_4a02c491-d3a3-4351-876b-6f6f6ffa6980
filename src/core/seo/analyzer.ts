/**
 * SEO Analyzer
 * Comprehensive SEO analysis and optimization engine
 */

export interface SEOAnalysisResult {
  score: number;
  metaDescription: string;
  titleTag: string;
  schemaMarkup: Record<string, any>;
  keywordAnalysis: KeywordAnalysis;
  internalLinkingSuggestions: InternalLinkSuggestion[];
  imageOptimizationSuggestions: ImageOptimizationSuggestion[];
  recommendations: SEORecommendation[];
  readabilityScore: number;
  contentMetrics: ContentMetrics;
}

export interface KeywordAnalysis {
  primaryKeyword: string;
  keywordDensity: number;
  keywordDistribution: KeywordDistribution[];
  relatedKeywords: string[];
  missingKeywords: string[];
  overOptimizedKeywords: string[];
}

export interface KeywordDistribution {
  keyword: string;
  count: number;
  density: number;
  positions: number[];
}

export interface InternalLinkSuggestion {
  anchorText: string;
  targetUrl: string;
  relevanceScore: number;
  context: string;
}

export interface ImageOptimizationSuggestion {
  imageUrl: string;
  altText: string;
  fileName: string;
  compressionRecommendation: string;
  sizeRecommendation: string;
}

export interface SEORecommendation {
  type: 'critical' | 'important' | 'suggestion';
  category: 'content' | 'technical' | 'keywords' | 'structure';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
}

export interface ContentMetrics {
  wordCount: number;
  sentenceCount: number;
  paragraphCount: number;
  headingStructure: HeadingStructure;
  averageSentenceLength: number;
  fleschKincaidScore: number;
}

export interface HeadingStructure {
  h1Count: number;
  h2Count: number;
  h3Count: number;
  h4Count: number;
  h5Count: number;
  h6Count: number;
  structure: HeadingElement[];
}

export interface HeadingElement {
  level: number;
  text: string;
  position: number;
}

export interface SEOConfig {
  targetKeywords?: string[];
  metaDescriptionLength?: number;
  titleLength?: number;
  enableSchemaMarkup?: boolean;
  enableInternalLinking?: boolean;
  enableImageOptimization?: boolean;
  contentLengthTarget?: number;
  keywordDensityTarget?: number;
  readabilityTarget?: 'basic' | 'intermediate' | 'advanced';
}

export class SEOAnalyzer {
  private config: SEOConfig;

  constructor(config: SEOConfig = {}) {
    this.config = {
      metaDescriptionLength: 160,
      titleLength: 60,
      enableSchemaMarkup: true,
      enableInternalLinking: true,
      enableImageOptimization: true,
      contentLengthTarget: 1500,
      keywordDensityTarget: 2.5,
      readabilityTarget: 'intermediate',
      ...config
    };
  }

  /**
   * Perform comprehensive SEO analysis on content
   */
  async analyzeContent(
    content: string,
    title: string,
    keywords: string[] = [],
    existingMetaDescription?: string
  ): Promise<SEOAnalysisResult> {
    console.log('🔍 Starting SEO analysis...');

    const contentMetrics = this.analyzeContentMetrics(content);
    const keywordAnalysis = this.analyzeKeywords(content, keywords);
    const readabilityScore = this.calculateReadabilityScore(content);
    
    const metaDescription = existingMetaDescription || 
      this.generateMetaDescription(content, keywords[0]);
    
    const titleTag = this.optimizeTitle(title, keywords[0]);
    const schemaMarkup = this.generateSchemaMarkup(title, metaDescription, content);
    
    const internalLinkingSuggestions = this.config.enableInternalLinking 
      ? this.generateInternalLinkingSuggestions(content, keywords)
      : [];
    
    const imageOptimizationSuggestions = this.config.enableImageOptimization
      ? this.analyzeImages(content)
      : [];

    const recommendations = this.generateRecommendations(
      contentMetrics,
      keywordAnalysis,
      readabilityScore,
      metaDescription,
      titleTag
    );

    const score = this.calculateOverallSEOScore(
      contentMetrics,
      keywordAnalysis,
      readabilityScore,
      metaDescription,
      titleTag
    );

    console.log(`✅ SEO analysis complete. Score: ${score}/100`);

    return {
      score,
      metaDescription,
      titleTag,
      schemaMarkup,
      keywordAnalysis,
      internalLinkingSuggestions,
      imageOptimizationSuggestions,
      recommendations,
      readabilityScore,
      contentMetrics
    };
  }

  /**
   * Analyze content metrics (word count, structure, etc.)
   */
  private analyzeContentMetrics(content: string): ContentMetrics {
    const words = content.match(/\b\w+\b/g) || [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    const headingStructure = this.analyzeHeadingStructure(content);
    const averageSentenceLength = words.length / sentences.length;
    const fleschKincaidScore = this.calculateFleschKincaid(content);

    return {
      wordCount: words.length,
      sentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
      headingStructure,
      averageSentenceLength,
      fleschKincaidScore
    };
  }

  /**
   * Analyze heading structure
   */
  private analyzeHeadingStructure(content: string): HeadingStructure {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings: HeadingElement[] = [];
    const counts = { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 };
    
    let match;
    while ((match = headingRegex.exec(content)) !== null) {
      const level = match[1].length;
      const text = match[2].trim();
      const position = match.index;
      
      headings.push({ level, text, position });
      counts[`h${level}` as keyof typeof counts]++;
    }

    return {
      h1Count: counts.h1,
      h2Count: counts.h2,
      h3Count: counts.h3,
      h4Count: counts.h4,
      h5Count: counts.h5,
      h6Count: counts.h6,
      structure: headings
    };
  }

  /**
   * Analyze keyword usage and distribution
   */
  private analyzeKeywords(content: string, targetKeywords: string[]): KeywordAnalysis {
    const contentLower = content.toLowerCase();
    const words = contentLower.match(/\b\w+\b/g) || [];
    const totalWords = words.length;

    const keywordDistribution: KeywordDistribution[] = [];
    const missingKeywords: string[] = [];
    const overOptimizedKeywords: string[] = [];

    for (const keyword of targetKeywords) {
      const keywordLower = keyword.toLowerCase();
      const regex = new RegExp(`\\b${keywordLower.replace(/\s+/g, '\\s+')}\\b`, 'gi');
      const matches = content.match(regex) || [];
      const count = matches.length;
      const density = (count / totalWords) * 100;
      
      // Find positions of keyword occurrences
      const positions: number[] = [];
      let match;
      while ((match = regex.exec(content)) !== null) {
        positions.push(match.index);
      }

      keywordDistribution.push({
        keyword,
        count,
        density,
        positions
      });

      // Check for optimization issues
      if (count === 0) {
        missingKeywords.push(keyword);
      } else if (density > (this.config.keywordDensityTarget || 2.5) * 1.5) {
        overOptimizedKeywords.push(keyword);
      }
    }

    const primaryKeyword = targetKeywords[0] || '';
    const primaryDensity = keywordDistribution[0]?.density || 0;
    
    // Generate related keywords (simplified)
    const relatedKeywords = this.generateRelatedKeywords(primaryKeyword);

    return {
      primaryKeyword,
      keywordDensity: primaryDensity,
      keywordDistribution,
      relatedKeywords,
      missingKeywords,
      overOptimizedKeywords
    };
  }

  /**
   * Generate meta description
   */
  private generateMetaDescription(content: string, primaryKeyword?: string): string {
    const maxLength = this.config.metaDescriptionLength || 160;
    
    // Extract first paragraph or first few sentences
    const firstParagraph = content.split('\n\n')[0];
    const sentences = firstParagraph.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let description = sentences[0]?.trim() || '';
    
    // Add more sentences if we have room
    for (let i = 1; i < sentences.length && description.length < maxLength - 50; i++) {
      const nextSentence = sentences[i].trim();
      if (description.length + nextSentence.length + 1 <= maxLength) {
        description += ' ' + nextSentence;
      }
    }

    // Ensure it includes the primary keyword if provided
    if (primaryKeyword && !description.toLowerCase().includes(primaryKeyword.toLowerCase())) {
      // Try to naturally incorporate the keyword
      description = `${primaryKeyword}: ${description}`;
    }

    // Truncate if too long
    if (description.length > maxLength) {
      description = description.substring(0, maxLength - 3) + '...';
    }

    return description;
  }

  /**
   * Optimize title tag
   */
  private optimizeTitle(title: string, primaryKeyword?: string): string {
    const maxLength = this.config.titleLength || 60;
    
    let optimizedTitle = title;

    // Ensure primary keyword is included
    if (primaryKeyword && !title.toLowerCase().includes(primaryKeyword.toLowerCase())) {
      optimizedTitle = `${primaryKeyword} - ${title}`;
    }

    // Truncate if too long
    if (optimizedTitle.length > maxLength) {
      optimizedTitle = optimizedTitle.substring(0, maxLength - 3) + '...';
    }

    return optimizedTitle;
  }

  /**
   * Generate schema markup
   */
  private generateSchemaMarkup(title: string, description: string, content: string): Record<string, any> {
    if (!this.config.enableSchemaMarkup) {
      return {};
    }

    const wordCount = (content.match(/\b\w+\b/g) || []).length;
    const readingTime = Math.ceil(wordCount / 200); // Average reading speed

    return {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": title,
      "description": description,
      "wordCount": wordCount,
      "timeRequired": `PT${readingTime}M`,
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
      "author": {
        "@type": "Organization",
        "name": "AuthencioCMS"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AuthencioCMS"
      }
    };
  }

  /**
   * Calculate readability score (simplified Flesch-Kincaid)
   */
  private calculateReadabilityScore(content: string): number {
    return this.calculateFleschKincaid(content);
  }

  /**
   * Calculate Flesch-Kincaid readability score
   */
  private calculateFleschKincaid(content: string): number {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.match(/\b\w+\b/g) || [];
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Count syllables in a word (simplified)
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = word.match(/[aeiouy]+/g);
    let syllableCount = vowels ? vowels.length : 1;
    
    // Adjust for silent e
    if (word.endsWith('e')) syllableCount--;
    
    return Math.max(1, syllableCount);
  }

  /**
   * Generate related keywords (simplified)
   */
  private generateRelatedKeywords(primaryKeyword: string): string[] {
    if (!primaryKeyword) return [];
    
    // This is a simplified implementation
    // In a real system, you'd use a keyword research API
    const variations = [
      `${primaryKeyword} guide`,
      `${primaryKeyword} tips`,
      `${primaryKeyword} best practices`,
      `how to ${primaryKeyword}`,
      `${primaryKeyword} tutorial`
    ];
    
    return variations.slice(0, 3);
  }

  /**
   * Generate internal linking suggestions
   */
  private generateInternalLinkingSuggestions(content: string, keywords: string[]): InternalLinkSuggestion[] {
    // This is a simplified implementation
    // In a real system, you'd analyze existing content and suggest relevant internal links
    const suggestions: InternalLinkSuggestion[] = [];
    
    for (const keyword of keywords.slice(0, 3)) {
      suggestions.push({
        anchorText: keyword,
        targetUrl: `/blog/${keyword.toLowerCase().replace(/\s+/g, '-')}`,
        relevanceScore: 0.8,
        context: `Consider linking to related content about ${keyword}`
      });
    }
    
    return suggestions;
  }

  /**
   * Analyze images for optimization
   */
  private analyzeImages(content: string): ImageOptimizationSuggestion[] {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const suggestions: ImageOptimizationSuggestion[] = [];
    
    let match;
    while ((match = imageRegex.exec(content)) !== null) {
      const altText = match[1];
      const imageUrl = match[2];
      const fileName = imageUrl.split('/').pop() || 'image';
      
      suggestions.push({
        imageUrl,
        altText: altText || `Optimized alt text for ${fileName}`,
        fileName,
        compressionRecommendation: 'Compress to WebP format for better performance',
        sizeRecommendation: 'Optimize for responsive display'
      });
    }
    
    return suggestions;
  }

  /**
   * Generate SEO recommendations
   */
  private generateRecommendations(
    contentMetrics: ContentMetrics,
    keywordAnalysis: KeywordAnalysis,
    readabilityScore: number,
    metaDescription: string,
    titleTag: string
  ): SEORecommendation[] {
    const recommendations: SEORecommendation[] = [];

    // Content length recommendations
    if (contentMetrics.wordCount < (this.config.contentLengthTarget || 1500)) {
      recommendations.push({
        type: 'important',
        category: 'content',
        title: 'Increase Content Length',
        description: `Content is ${contentMetrics.wordCount} words. Consider expanding to ${this.config.contentLengthTarget} words for better SEO.`,
        impact: 'medium',
        effort: 'medium'
      });
    }

    // Heading structure recommendations
    if (contentMetrics.headingStructure.h1Count === 0) {
      recommendations.push({
        type: 'critical',
        category: 'structure',
        title: 'Add H1 Heading',
        description: 'Content should have exactly one H1 heading for proper structure.',
        impact: 'high',
        effort: 'low'
      });
    }

    // Keyword recommendations
    if (keywordAnalysis.missingKeywords.length > 0) {
      recommendations.push({
        type: 'important',
        category: 'keywords',
        title: 'Include Missing Keywords',
        description: `Consider including these keywords: ${keywordAnalysis.missingKeywords.join(', ')}`,
        impact: 'medium',
        effort: 'low'
      });
    }

    // Readability recommendations
    if (readabilityScore < 60) {
      recommendations.push({
        type: 'suggestion',
        category: 'content',
        title: 'Improve Readability',
        description: 'Content readability could be improved with shorter sentences and simpler words.',
        impact: 'medium',
        effort: 'medium'
      });
    }

    return recommendations;
  }

  /**
   * Calculate overall SEO score
   */
  private calculateOverallSEOScore(
    contentMetrics: ContentMetrics,
    keywordAnalysis: KeywordAnalysis,
    readabilityScore: number,
    metaDescription: string,
    titleTag: string
  ): number {
    let score = 0;

    // Content metrics (30 points)
    if (contentMetrics.wordCount >= (this.config.contentLengthTarget || 1500)) score += 10;
    if (contentMetrics.headingStructure.h1Count === 1) score += 10;
    if (contentMetrics.headingStructure.h2Count >= 2) score += 10;

    // Keyword optimization (25 points)
    if (keywordAnalysis.keywordDensity >= 1 && keywordAnalysis.keywordDensity <= 3) score += 15;
    if (keywordAnalysis.missingKeywords.length === 0) score += 10;

    // Readability (20 points)
    if (readabilityScore >= 60) score += 20;
    else if (readabilityScore >= 30) score += 10;

    // Meta elements (25 points)
    if (metaDescription.length >= 120 && metaDescription.length <= 160) score += 15;
    if (titleTag.length >= 30 && titleTag.length <= 60) score += 10;

    return Math.min(100, score);
  }
}
