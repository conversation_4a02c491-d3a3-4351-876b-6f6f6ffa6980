/**
 * CMS Publishing API
 * Publishes approved workflow artifacts to Payload CMS
 */

import { NextRequest, NextResponse } from 'next/server';
import { PayloadIntegration, PayloadPublishRequest } from '@/core/cms/payload-integration';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      artifactId,
      title,
      content,
      type,
      executionId,
      stepId,
      metadata,
      status = 'published',
      categories = [],
      tags = [],
      author,
      seoMetadata = {}
    } = body;

    // Validate required fields
    if (!artifactId || !title || !content) {
      return NextResponse.json(
        { error: 'Missing required fields: artifactId, title, content' },
        { status: 400 }
      );
    }

    // Initialize Payload integration
    const payloadIntegration = new PayloadIntegration();

    // Prepare publish request
    const publishRequest: PayloadPublishRequest = {
      title,
      content,
      status: status as 'draft' | 'published',
      meta: {
        description: metadata?.description || seoMetadata?.description || `Generated content from workflow execution ${executionId}`,
        keywords: metadata?.keywords || seoMetadata?.keywords?.join(', ') || '',
      },
      categories,
      tags,
      author: author || 'workflow-system',
      publishedDate: status === 'published' ? new Date().toISOString() : undefined,
      workflow: {
        executionId,
        templateId: metadata?.templateId || '',
        generatedAt: new Date().toISOString(),
        agentConsultations: metadata?.agentConsultations
      },
      seoMetadata: {
        title: seoMetadata?.title || title,
        description: seoMetadata?.description || metadata?.description || '',
        keywords: seoMetadata?.keywords || [],
        schemaMarkup: seoMetadata?.schemaMarkup
      }
    };

    // Customize based on artifact type
    if (type === 'blog_post' || type === 'content_draft') {
      // Parse JSON content if it's structured
      if (typeof content === 'string' && content.startsWith('{')) {
        try {
          const parsed = JSON.parse(content);
          publishRequest.title = parsed.title || title;
          publishRequest.content = parsed.content || content;
          if (parsed.meta_description) {
            publishRequest.meta!.description = parsed.meta_description;
          }
        } catch (e) {
          // Keep original content if parsing fails
        }
      }
    }

    console.log(`📝 Publishing ${type || 'content'} to Payload CMS: "${publishRequest.title}"`);

    // Publish using Payload integration
    const result = await payloadIntegration.publishContent(publishRequest);

    console.log(`✅ Successfully published to Payload CMS with ID: ${result.id}`);

    // Return success with the created document info
    return NextResponse.json({
      success: true,
      data: {
        id: result.id,
        url: result.url,
        collection: result.collection,
        title: publishRequest.title,
        status: result.status,
        publishedAt: result.publishedAt,
        slug: result.slug,
        artifactId,
        executionId,
        stepId
      },
      message: `Successfully published to Payload CMS`
    });

  } catch (error) {
    console.error('CMS publish error:', error);

    // Handle specific Payload errors
    if (error instanceof Error) {
      if (error.message.includes('Collection')) {
        return NextResponse.json(
          { error: 'Invalid collection or configuration error', details: error.message },
          { status: 400 }
        );
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { error: 'Validation error', details: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Failed to publish to CMS',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check publishing status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const artifactId = searchParams.get('artifactId');

    if (!artifactId) {
      return NextResponse.json(
        { error: 'artifactId parameter required' },
        { status: 400 }
      );
    }

    // Get Payload instance
    const payload = await getPayload({ config });

    // Search for documents with this artifact ID
    const collections = ['posts', 'research']; // Add more collections as needed
    const results = [];

    for (const collection of collections) {
      try {
        const docs = await payload.find({
          collection,
          where: {
            'workflowMetadata.artifactId': {
              equals: artifactId
            }
          }
        });
        
        results.push(...docs.docs.map(doc => ({
          ...doc,
          collection
        })));
      } catch (e) {
        // Collection might not exist, continue
        console.warn(`Collection ${collection} not found or accessible`);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        artifactId,
        published: results.length > 0,
        documents: results.map(doc => ({
          id: doc.id,
          collection: doc.collection,
          title: doc.title,
          status: doc.status,
          url: `/admin/collections/${doc.collection}/${doc.id}`,
          createdAt: doc.createdAt
        }))
      }
    });

  } catch (error) {
    console.error('CMS status check error:', error);

    return NextResponse.json(
      {
        error: 'Failed to check publishing status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
