/**
 * Workflow Events API - Server-Sent Events (SSE)
 * Provides real-time workflow progress updates via SSE
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../core/workflow/singleton';
import { getRealTimeBroadcaster } from '../../../../core/events/real-time-broadcaster';

/**
 * GET /api/workflow/events
 * Establish SSE connection for real-time workflow updates
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const executionId = searchParams.get('executionId');
  const clientId = searchParams.get('clientId') || `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  console.log(`📡 SSE connection request for execution: ${executionId}, client: ${clientId}`);

  // Validate execution ID if provided
  if (executionId) {
    try {
      const workflowEngine = getWorkflowEngine();
      const execution = await workflowEngine.getExecution(executionId);
      if (!execution) {
        return NextResponse.json({
          error: 'Execution not found'
        }, { status: 404 });
      }
    } catch (error) {
      console.error(`❌ Error validating execution ${executionId}:`, error);
      return NextResponse.json({
        error: 'Failed to validate execution'
      }, { status: 500 });
    }
  }

  // Create SSE response
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      console.log(`🔗 SSE connection established for client ${clientId}`);

      // Get real-time broadcaster
      const broadcaster = getRealTimeBroadcaster();

      // Send initial connection message
      const connectionMessage = {
        type: 'connection',
        data: {
          clientId,
          timestamp: new Date().toISOString(),
          message: 'Connected to workflow events stream'
        }
      };

      controller.enqueue(encoder.encode(`data: ${JSON.stringify(connectionMessage)}\n\n`));

      // Send current execution status if executionId provided
      if (executionId) {
        getWorkflowEngine().getExecution(executionId).then(execution => {
          if (execution) {
            const statusMessage = {
              type: 'execution_status',
              data: {
                executionId,
                status: execution.status,
                progress: execution.progress,
                currentStep: execution.currentStep,
                stepResults: Object.keys(execution.stepResults).map(stepId => ({
                  stepId,
                  status: execution.stepResults[stepId].status,
                  completedAt: execution.stepResults[stepId].completedAt
                })),
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(statusMessage)}\n\n`));
          }
        }).catch(error => {
          console.error(`❌ Error sending initial status:`, error);
        });
      }

      // Set up event listeners
      const eventHandlers = {
        // Workflow events
        workflow_started: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'workflow_started',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        workflow_completed: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'workflow_completed',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        workflow_failed: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'workflow_failed',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        workflow_paused: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'workflow_paused',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        // Step events
        step_started: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'step_started',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        step_completed: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'step_completed',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        step_failed: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'step_failed',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        // Progress events
        progress_update: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'progress_update',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        // Review events
        review_requested: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'review_requested',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        },

        review_completed: (data: any) => {
          if (!executionId || data.executionId === executionId) {
            const message = {
              type: 'review_completed',
              data: {
                ...data,
                timestamp: new Date().toISOString()
              }
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(message)}\n\n`));
          }
        }
      };

      // Register event handlers
      Object.entries(eventHandlers).forEach(([eventType, handler]) => {
        broadcaster.on(eventType, handler);
      });

      // Store cleanup function
      const cleanup = () => {
        console.log(`🔌 SSE connection closed for client ${clientId}`);
        
        // Remove event listeners
        Object.entries(eventHandlers).forEach(([eventType, handler]) => {
          broadcaster.off(eventType, handler);
        });

        // Remove client from broadcaster
        broadcaster.removeClient(clientId);
      };

      // Add client to broadcaster
      broadcaster.addClient(clientId, {
        executionId,
        connectedAt: new Date().toISOString(),
        cleanup
      });

      // Handle connection close
      request.signal?.addEventListener('abort', cleanup);

      // Send periodic heartbeat
      const heartbeatInterval = setInterval(() => {
        try {
          const heartbeat = {
            type: 'heartbeat',
            data: {
              timestamp: new Date().toISOString(),
              clientId
            }
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(heartbeat)}\n\n`));
        } catch (error) {
          console.log(`💔 Heartbeat failed for client ${clientId}, cleaning up`);
          clearInterval(heartbeatInterval);
          cleanup();
        }
      }, 30000); // 30 seconds

      // Store interval for cleanup
      if (!request.signal?.aborted) {
        request.signal?.addEventListener('abort', () => {
          clearInterval(heartbeatInterval);
        });
      }
    },

    cancel() {
      console.log(`🔌 SSE stream cancelled for client ${clientId}`);
    }
  });

  // Return SSE response
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}

/**
 * POST /api/workflow/events
 * Send custom event to connected clients
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { eventType, data, executionId } = body;

    if (!eventType || !data) {
      return NextResponse.json({
        error: 'Missing required fields: eventType, data'
      }, { status: 400 });
    }

    const broadcaster = getRealTimeBroadcaster();
    
    // Broadcast the event
    broadcaster.broadcast(eventType, {
      ...data,
      executionId,
      timestamp: new Date().toISOString()
    });

    console.log(`📡 Custom event broadcasted: ${eventType} for execution ${executionId || 'all'}`);

    return NextResponse.json({
      success: true,
      message: 'Event broadcasted successfully'
    });

  } catch (error) {
    console.error(`❌ Error broadcasting custom event:`, error);
    return NextResponse.json({
      error: 'Failed to broadcast event'
    }, { status: 500 });
  }
}
