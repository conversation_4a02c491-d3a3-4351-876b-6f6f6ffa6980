/**
 * Simplified Workflow Engine
 * Basic workflow execution without complex orchestration
 * Fixed execution scope issue
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IWorkflowEngine,
  Workflow,
  WorkflowExecution,
  WorkflowStep,
  StepResult,
  StepType,
  ExecutionStatus,
  StepStatus,
  WorkflowFilters,
  ReviewDecision,
  VariableContext,
  ExecutionMetadata,
  WorkflowArtifact,
  ArtifactType,
  ArtifactStatus,
  ApprovalDecision,
  ApprovalStatus,
  StepCondition,
  ConditionalRule,
  ApprovalGate
} from './types';
import { ISimplifiedStateStore, ContentItem, ContentType, ContentStatus } from '../state/types';
import { SimplifiedStateStore } from '../state/store';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';
import { ReviewManager } from '../review/manager';
import { userManager } from '../../utils/user-manager';
import { errorHandler } from '../utils/error-handler';
import { SEOAnalyzer, SEOAnalysisResult } from '../seo/analyzer';
import { ResultsCompiler, WorkflowResultsPackage } from '../results/compiler';
import { MetricsCalculator } from '../metrics/calculator';
import { PayloadIntegration, PayloadPublishRequest } from '../cms/payload-integration';
import { WorkflowCompletionHandler, CompletionReport, CompletionConfig } from './completion-handler';
import { CompletionNotifier, NotificationPayload } from '../notifications/completion-notifier';
import { FeedbackProcessor, ProcessedFeedback } from '../review/feedback-processor';
import { WorkflowErrorHandler, ErrorType, ErrorSeverity, RecoveryStrategy } from '../error/error-handler';
import { WorkflowRecoveryManager, RecoveryPlan } from '../error/recovery-manager';
import { getRealTimeBroadcaster } from '../events/real-time-broadcaster';
import {
  workflowArtifactToContentItem,
  contentItemToWorkflowArtifact,
  normalizeStepResult,
  normalizeExecutionMetadata,
  validateTypeConversion,
  isWorkflowArtifact,
  isContentItem
} from '../utils/type-converters';
import { EnhancedAIGenerationStep } from './enhanced-ai-generation-step';
import { getConsultationService, getEventBus } from './singleton';
import { WorkflowEventBus, WorkflowEventType, WorkflowEvent } from './event-bus';

export class WorkflowEngine implements IWorkflowEngine {
  private reviewManager: ReviewManager;
  private enhancedAIStep: EnhancedAIGenerationStep;
  private eventBus: WorkflowEventBus;
  private errorHandler: WorkflowErrorHandler;
  private recoveryManager: WorkflowRecoveryManager;
  private realTimeBroadcaster: any;

  constructor(
    private stateStore: ISimplifiedStateStore,
    private aiManager: AIModelManager,
    private reviewSystem: SimplifiedReviewSystem
  ) {
    this.reviewManager = new ReviewManager();
    this.enhancedAIStep = new EnhancedAIGenerationStep();
    this.eventBus = getEventBus();
    this.errorHandler = new WorkflowErrorHandler({
      enableRetry: true,
      maxRetries: 3,
      enableFallback: true,
      enableLogging: true,
      enableNotifications: true,
      notificationThreshold: ErrorSeverity.HIGH,
      enableRecovery: true
    });
    this.recoveryManager = new WorkflowRecoveryManager();
    this.realTimeBroadcaster = getRealTimeBroadcaster();

    // Set up event handlers for workflow coordination
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Handle step completion events to continue workflow execution
    this.eventBus.on(WorkflowEventType.STEP_COMPLETED, async (event: WorkflowEvent) => {
      if (event.executionId) {
        console.log(`📡 Received step completion event for execution ${event.executionId}, step ${event.stepId}`);
        // Continue workflow execution after a brief delay to prevent stack overflow
        setImmediate(async () => {
          try {
            console.log(`🔄 About to call executeWorkflowSteps for ${event.executionId}`);
            await this.executeWorkflowSteps(event.executionId!);
            console.log(`✅ executeWorkflowSteps completed for ${event.executionId}`);
          } catch (error) {
            console.error(`❌ Workflow execution ${event.executionId} failed after step completion:`, error);
            console.error(`❌ Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
            await this.markExecutionFailed(event.executionId!, error);
          }
        });
      }
    });

    // Handle workflow resumption events
    this.eventBus.on(WorkflowEventType.EXECUTION_CONTINUE, async (event: WorkflowEvent) => {
      if (event.executionId) {
        console.log(`📡 Received execution continue event for ${event.executionId}`);
        setImmediate(() => {
          this.executeWorkflowSteps(event.executionId!).catch(error => {
            console.error(`❌ Workflow execution ${event.executionId} failed on continue:`, error);
            this.markExecutionFailed(event.executionId!, error);
          });
        });
      }
    });
  }

  // Workflow management
  async createWorkflow(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      // Validate workflow data
      this.validateWorkflowData(workflow);

      const id = uuidv4();
      const now = new Date().toISOString();

      const newWorkflow: Workflow = {
        ...workflow,
        id,
        createdAt: now,
        updatedAt: now
      };

      console.log(`🔄 Creating workflow: ${id} (${workflow.name})`);
      await this.stateStore.setWorkflow(newWorkflow);

      // Verify the workflow was created with retry logic for concurrent operations
      let verifyWorkflow = null;
      let retries = 3;

      while (retries > 0 && !verifyWorkflow) {
        await new Promise(resolve => setTimeout(resolve, 10)); // Small delay
        verifyWorkflow = await this.stateStore.getWorkflow(id);
        retries--;
      }

      if (!verifyWorkflow) {
        throw new Error(`Failed to verify workflow creation after retries: ${id}`);
      }

      console.log(`✅ Workflow created and verified: ${id} (${workflow.name})`);
      return id;
    } catch (error) {
      console.error(`❌ Failed to create workflow: ${workflow.name}`, error);

      // If it's a validation error, throw it directly without wrapping
      if (error instanceof Error && (
        error.message.includes('required') ||
        error.message.includes('must be') ||
        error.message.includes('cannot be empty')
      )) {
        throw error;
      }

      const standardError = errorHandler.handleError(error, {
        operation: 'createWorkflow',
        workflowName: workflow.name,
        workflowSteps: workflow.steps?.length || 0
      });
      throw standardError;
    }
  }

  private validateWorkflowData(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): void {
    if (!workflow.name || workflow.name.trim() === '') {
      throw new Error('Workflow name is required and cannot be empty');
    }

    if (!workflow.description || workflow.description.trim() === '') {
      throw new Error('Workflow description is required and cannot be empty');
    }

    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      throw new Error('Workflow steps must be an array');
    }

    if (!workflow.metadata) {
      throw new Error('Workflow metadata is required');
    }

    // Validate metadata fields
    if (!workflow.metadata.category || workflow.metadata.category.trim() === '') {
      throw new Error('Workflow category is required');
    }

    if (!workflow.metadata.difficulty || !['easy', 'medium', 'hard'].includes(workflow.metadata.difficulty)) {
      throw new Error('Workflow difficulty must be easy, medium, or hard');
    }

    if (typeof workflow.metadata.estimatedTime !== 'number' || workflow.metadata.estimatedTime <= 0) {
      throw new Error('Workflow estimated time must be a positive number');
    }
  }

  async getWorkflow(id: string): Promise<Workflow | null> {
    try {
      return await this.stateStore.getWorkflow(id);
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'getWorkflow',
        workflowId: id
      });
      throw standardError;
    }
  }

  async updateWorkflow(id: string, updates: Partial<Workflow>): Promise<void> {
    const workflow = await this.getWorkflow(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }

    const updatedWorkflow: Workflow = {
      ...workflow,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.stateStore.setWorkflow(updatedWorkflow);
  }

  async deleteWorkflow(id: string): Promise<void> {
    // In a full implementation, this would remove the workflow
    // For now, just mark as deleted or implement soft delete
    throw new Error('Delete workflow not implemented yet');
  }

  async listWorkflows(filters?: WorkflowFilters): Promise<Workflow[]> {
    const workflows = await this.stateStore.getAllWorkflows();

    if (!filters) {
      return workflows;
    }

    return workflows.filter(workflow => {
      if (filters.category && workflow.metadata.category !== filters.category) {
        return false;
      }
      if (filters.difficulty && workflow.metadata.difficulty !== filters.difficulty) {
        return false;
      }
      if (filters.featured !== undefined && workflow.metadata.featured !== filters.featured) {
        return false;
      }
      if (filters.tags && !filters.tags.some(tag => workflow.metadata.tags.includes(tag))) {
        return false;
      }
      return true;
    });
  }

  // Execution management
  async executeWorkflow(
    workflowId: string,
    inputs: Record<string, any>,
    metadata?: Partial<ExecutionMetadata>
  ): Promise<string> {
    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        const error = errorHandler.createError(
          ErrorType.WORKFLOW,
          'WORKFLOW_NOT_FOUND',
          `Workflow ${workflowId} not found`,
          { workflowId }
        );
        throw error;
      }

      const executionId = uuidv4();
      const now = new Date().toISOString();

      const execution: WorkflowExecution = {
        id: executionId,
        workflowId,
        status: ExecutionStatus.RUNNING,
        inputs,
        outputs: {},
        stepResults: {},
        progress: 0,
        startedAt: now,
        metadata: normalizeExecutionMetadata(metadata || {})
      };

      await this.stateStore.setExecution(execution);

      console.log(`🚀 Workflow execution started: ${executionId} (${workflow.name})`);

      // Emit workflow started event
      await this.eventBus.emit(
        WorkflowEventType.WORKFLOW_STARTED,
        {
          workflowId,
          workflowName: workflow.name,
          inputs
        },
        'workflow-engine',
        { executionId }
      );

      // Broadcast real-time workflow started event
      this.realTimeBroadcaster.broadcastWorkflowStarted(executionId, workflowId, {
        workflowName: workflow.name,
        totalSteps: workflow.steps.length,
        inputs
      });

      // Start execution asynchronously
      this.executeWorkflowSteps(executionId).catch(error => {
        const standardError = errorHandler.handleError(error, {
          operation: 'executeWorkflowSteps',
          executionId,
          workflowId
        });
        console.error(`❌ Workflow execution ${executionId} failed:`, standardError.message);
        this.markExecutionFailed(executionId, standardError);
      });

      return executionId;
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'executeWorkflow',
        workflowId,
        inputs
      });
      throw standardError;
    }
  }

  async getExecution(id: string): Promise<WorkflowExecution | null> {
    return await this.stateStore.getExecution(id);
  }

  async pauseExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.PAUSED
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  async resumeExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.RUNNING
    };

    await this.stateStore.setExecution(updatedExecution);

    // Resume execution
    this.executeWorkflowSteps(id).catch(error => {
      console.error(`Workflow execution ${id} failed on resume:`, error);
      this.markExecutionFailed(id, error);
    });
  }

  async cancelExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.CANCELLED,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  // Step execution
  async executeStep(executionId: string, stepId: string): Promise<StepResult> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const workflow = await this.getWorkflow(execution.workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${execution.workflowId} not found`);
    }

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error(`Step ${stepId} not found in workflow`);
    }

    return await this.executeWorkflowStep(execution, step);
  }

  async retryStep(executionId: string, stepId: string): Promise<StepResult> {
    // Reset step status and re-execute
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Remove previous step result
    const updatedExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: undefined
      }
    };
    delete updatedExecution.stepResults[stepId];

    await this.stateStore.setExecution(updatedExecution);

    return await this.executeStep(executionId, stepId);
  }

  // Review handling
  async submitReview(executionId: string, stepId: string, decision: ReviewDecision): Promise<void> {
    // This would be called by the review system
    // For now, just update the step status
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const stepResult = execution.stepResults[stepId];
    if (!stepResult) {
      throw new Error(`Step result ${stepId} not found`);
    }

    const updatedStepResult: StepResult = {
      ...stepResult,
      status: decision.approved ? StepStatus.COMPLETED : StepStatus.FAILED,
      completedAt: new Date().toISOString(),
      outputs: {
        ...stepResult.outputs,
        review_decision: decision.approved ? 'approved' : 'rejected',
        review_feedback: decision.feedback
      }
    };

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: updatedStepResult
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // Continue execution if approved
    if (decision.approved) {
      this.executeWorkflowSteps(executionId).catch(error => {
        console.error(`Workflow execution ${executionId} failed after review:`, error);
        this.markExecutionFailed(executionId, error);
      });
    }
  }

  /**
   * Handle review completion (both approval and rejection) - NEW METHOD
   * This is the main entry point for review decisions that properly handles workflow continuation
   */
  async handleReviewCompletion(
    executionId: string,
    stepId: string,
    reviewDecision: { decision: 'approve' | 'reject', feedback?: string, reviewer?: string }
  ): Promise<void> {
    console.log(`📝 Handling review completion for ${executionId}/${stepId}: ${reviewDecision.decision}`);

    const execution = await this.getExecution(executionId);
    if (!execution) throw new Error(`Execution ${executionId} not found`);

    const stepResult = execution.stepResults[stepId];
    if (!stepResult) throw new Error(`Step result ${stepId} not found`);

    if (reviewDecision.decision === 'approve') {
      // ✅ APPROVAL: Update both review AND approval gate
      console.log(`✅ Review approved - updating approval gate and continuing workflow`);

      // 1. Mark review step as completed
      stepResult.status = StepStatus.COMPLETED;
      stepResult.outputs = { ...stepResult.outputs, review_decision: 'approved' };
      stepResult.completedAt = new Date().toISOString();

      // 2. Update approval gate if artifact exists
      if (stepResult.artifactId) {
        console.log(`🔓 Updating approval gate for artifact ${stepResult.artifactId}`);
        await this.updateApprovalGateStatus(stepResult.artifactId, 'approved', reviewDecision.reviewer);
      }

      // 3. Calculate progress for the updated execution
      const workflow = await this.getWorkflow(execution.workflowId);
      const totalSteps = workflow?.steps.length || 1;
      const completedSteps = Object.values(execution.stepResults).filter(
        result => result.status === StepStatus.COMPLETED || result.status === StepStatus.APPROVED
      ).length;
      const newProgress = Math.round((completedSteps / totalSteps) * 100);

      // 4. Update execution status to RUNNING and save
      const updatedExecution: WorkflowExecution = {
        ...execution,
        status: ExecutionStatus.RUNNING, // ✅ CRITICAL FIX: Set status back to RUNNING
        progress: newProgress,
        stepResults: {
          ...execution.stepResults,
          [stepId]: stepResult
        }
      };

      await this.stateStore.setExecution(updatedExecution);
      console.log(`🔧 Execution ${executionId} updated: status=RUNNING, progress=${newProgress}%`);

      console.log(`Continuing workflow execution ${executionId} after step ${stepId}`);
      console.log(`Review ${stepResult.artifactId} completed with decision: ${reviewDecision.decision}`);

      // 5. Verify the step status is properly updated before continuing
      const verifyExecution = await this.getExecution(executionId);
      if (verifyExecution) {
        console.log(`🔍 Verification - Step ${stepId} status: ${verifyExecution.stepResults[stepId]?.status}`);
        console.log(`🔍 Verification - Execution status: ${verifyExecution.status}`);
      }

      // 6. Continue workflow execution immediately in next tick to avoid race conditions
      console.log(`🚀 Approval complete - continuing workflow execution ${executionId}`);
      setImmediate(async () => {
        try {
          console.log(`🔄 Resuming workflow execution ${executionId} after review approval`);
          await this.executeWorkflowSteps(executionId);
        } catch (error) {
          console.error(`❌ Failed to continue workflow ${executionId} after review approval:`, error);
          await this.markExecutionFailed(executionId, error);
        }
      });

    } else {
      // ❌ REJECTION: Keep approval gate waiting, handle regeneration
      console.log(`❌ Review rejected - keeping approval gate active, processing feedback`);

      if (reviewDecision.feedback && reviewDecision.feedback.trim().length > 10) {
        console.log(`🔄 Review rejected with actionable feedback - triggering regeneration`);
        // Keep approval gate in waiting state, regenerate content
        await this.handleRejectionWithRegeneration(execution, stepId, reviewDecision);
      } else {
        console.log(`⏭️ Review rejected without actionable feedback - continuing to next step`);
        // Mark approval gate as rejected and continue
        if (stepResult.artifactId) {
          await this.updateApprovalGateStatus(stepResult.artifactId, 'rejected', reviewDecision.reviewer);
        }
        await this.handleRejectionWithoutRegeneration(execution, stepId, reviewDecision);
      }
    }
  }

  /**
   * Handle rejection with regeneration based on feedback
   */
  async handleRejectionWithRegeneration(
    execution: WorkflowExecution,
    stepId: string,
    reviewDecision: any
  ): Promise<void> {
    try {
      console.log(`🔄 Processing rejection with regeneration for step ${stepId}`);

      const stepResult = execution.stepResults[stepId];
      const originalContent = stepResult.outputs?.blog_content || stepResult.outputs?.content || '';

      if (!originalContent) {
        console.log(`⚠️ No content found for regeneration, continuing to next step`);
        await this.handleRejectionWithoutRegeneration(execution, stepId, reviewDecision);
        return;
      }

      // Step 1: Process feedback using enhanced feedback processor
      const feedbackProcessor = new FeedbackProcessor();
      const processedFeedback: ProcessedFeedback = await feedbackProcessor.processFeedback(
        reviewDecision.feedback || '',
        originalContent,
        'blog_post'
      );

      console.log(`📊 Feedback analysis: ${processedFeedback.analysis.suggestedAction} (confidence: ${Math.round(processedFeedback.analysis.confidence * 100)}%)`);
      console.log(`📊 Priority: ${processedFeedback.analysis.priority}, Categories: ${processedFeedback.analysis.categories.map(c => c.type).join(', ')}`);

      // If feedback analysis suggests skipping regeneration, continue to next step
      if (processedFeedback.analysis.suggestedAction === 'reject' || !processedFeedback.analysis.isActionable) {
        console.log(`⏭️ Feedback analysis suggests skipping regeneration`);
        await this.handleRejectionWithoutRegeneration(execution, stepId, reviewDecision);
        return;
      }

      // If manual review is suggested, mark for manual review but continue for now
      if (processedFeedback.analysis.suggestedAction === 'manual_review') {
        console.log(`👥 Feedback analysis suggests manual review - continuing with regeneration attempt`);
      }

      // Step 2: Attempt content regeneration based on processed feedback
      if (processedFeedback.analysis.suggestedAction === 'regenerate') {
        console.log(`🔄 Attempting content regeneration based on feedback analysis`);

        try {
          // Generate regeneration prompt from processed feedback
          const regenerationPrompt = processedFeedback.regenerationPrompt ||
            this.generateRegenerationPrompt(processedFeedback, originalContent);

          // Attempt to regenerate content using AI generation
          const regeneratedContent = await this.regenerateContentWithFeedback(
            originalContent,
            regenerationPrompt,
            processedFeedback.analysis
          );

          if (regeneratedContent && regeneratedContent.trim().length > 100) {
            console.log(`✅ Content regeneration successful`);

            // Update step result with regenerated content
            stepResult.outputs = {
              ...stepResult.outputs,
              blog_content: regeneratedContent,
              content: regeneratedContent,
              review_decision: 'regenerated',
              regeneration_applied: true,
              feedback_analysis: processedFeedback.analysis,
              processed_feedback: processedFeedback,
              original_content: originalContent
            };

            stepResult.metadata = {
              ...stepResult.metadata,
              regenerated: true,
              userFeedbackIncorporated: true,
              regenerationTimestamp: new Date().toISOString(),
              feedbackProcessingTime: processedFeedback.metadata.processingTime,
              improvementAreas: processedFeedback.analysis.improvementAreas,
              regenerationStrategy: processedFeedback.analysis.regenerationStrategy
            };

            // Mark step as completed
            stepResult.status = StepStatus.COMPLETED;
            stepResult.completedAt = new Date().toISOString();

            // Update approval gate to approved since content was regenerated successfully
            if (stepResult.artifactId) {
              console.log(`🔓 Updating approval gate for regenerated content ${stepResult.artifactId}`);
              await this.updateApprovalGateStatus(stepResult.artifactId, 'approved', 'regeneration-system');
            }

            // Update execution
            const updatedExecution: WorkflowExecution = {
              ...execution,
              status: ExecutionStatus.RUNNING,
              stepResults: {
                ...execution.stepResults,
                [stepId]: stepResult
              }
            };

            await this.stateStore.setExecution(updatedExecution);

            console.log(`🚀 Regeneration complete - continuing workflow execution ${execution.id}`);

            // Continue workflow execution
            setImmediate(async () => {
              try {
                await this.executeWorkflowSteps(execution.id);
              } catch (error) {
                console.error(`❌ Failed to continue workflow ${execution.id} after regeneration:`, error);
                await this.markExecutionFailed(execution.id, error);
              }
            });

            return;
          } else {
            console.log(`⚠️ Regenerated content is too short or empty, falling back`);
          }
        } catch (error) {
          console.error(`❌ Content regeneration failed:`, error);
        }
      }



      // Fallback to continuing without regeneration
      console.log(`⚠️ Regeneration failed, continuing to next step`);
      await this.handleRejectionWithoutRegeneration(execution, stepId, reviewDecision);

    } catch (error) {
      console.error(`❌ Error during regeneration process:`, error);
      await this.handleRejectionWithoutRegeneration(execution, stepId, reviewDecision);
    }
  }

  /**
   * Handle rejection without regeneration - continue to next step
   */
  async handleRejectionWithoutRegeneration(
    execution: WorkflowExecution,
    stepId: string,
    reviewDecision: any
  ): Promise<void> {
    console.log(`⏭️ Handling rejection without regeneration for step ${stepId}`);

    const stepResult = execution.stepResults[stepId];

    // Mark step as completed with rejection status
    stepResult.status = StepStatus.COMPLETED;
    stepResult.outputs = {
      ...stepResult.outputs,
      review_decision: 'rejected_continue',
      rejection_reason: reviewDecision.feedback || 'No feedback provided'
    };
    stepResult.completedAt = new Date().toISOString();

    await this.stateStore.setExecution(execution);

    console.log(`🚀 Rejection processed - continuing workflow execution ${execution.id}`);
    // Continue to next step
    this.executeWorkflowSteps(execution.id).catch(error => {
      console.error(`Workflow execution ${execution.id} failed after rejection:`, error);
      this.markExecutionFailed(execution.id, error);
    });
  }

  /**
   * Update approval gate status for an artifact
   * This integrates the review system with the approval gate system
   */
  private async updateApprovalGateStatus(
    artifactId: string,
    status: 'approved' | 'rejected' | 'waiting',
    reviewer?: string
  ): Promise<void> {
    try {
      console.log(`🔓 Updating approval gate for artifact ${artifactId}: ${status}`);

      // Get the artifact to find execution and step info
      const artifact = await this.getArtifact(artifactId);
      if (!artifact) {
        console.warn(`⚠️ Artifact ${artifactId} not found for approval gate update`);
        return;
      }

      // Update artifact status
      const updates: Partial<WorkflowArtifact> = {
        status: status === 'approved' ? ArtifactStatus.APPROVED :
                status === 'rejected' ? ArtifactStatus.REJECTED :
                ArtifactStatus.PENDING_APPROVAL,
        updatedAt: new Date().toISOString()
      };

      if (status === 'approved' && reviewer) {
        updates.approvedBy = reviewer;
        updates.approvedAt = new Date().toISOString();
      } else if (status === 'rejected' && reviewer) {
        updates.rejectedBy = reviewer;
        updates.rejectedAt = new Date().toISOString();
      }

      await this.updateArtifact(artifactId, updates);

      // Update execution step status to reflect approval gate status
      const execution = await this.getExecution(artifact.executionId);
      if (execution && execution.stepResults[artifact.stepId]) {
        const stepResult = execution.stepResults[artifact.stepId];

        if (status === 'approved') {
          stepResult.status = StepStatus.APPROVED;
          stepResult.approvedBy = reviewer;
          stepResult.approvedAt = new Date().toISOString();
          stepResult.metadata = {
            ...stepResult.metadata,
            approvalGateStatus: 'approved',
            approvedBy: reviewer,
            approvedAt: new Date().toISOString()
          };
        } else if (status === 'rejected') {
          stepResult.status = StepStatus.REJECTED;
          stepResult.metadata = {
            ...stepResult.metadata,
            approvalGateStatus: 'rejected',
            rejectedBy: reviewer,
            rejectedAt: new Date().toISOString()
          };
        } else {
          stepResult.status = StepStatus.WAITING_APPROVAL;
          stepResult.metadata = {
            ...stepResult.metadata,
            approvalGateStatus: 'waiting'
          };
        }

        await this.stateStore.setExecution(execution);
        console.log(`✅ Approval gate updated: ${artifactId} -> ${status}`);
      }

    } catch (error) {
      console.error(`❌ Error updating approval gate for ${artifactId}:`, error);
    }
  }

  /**
   * Generate regeneration prompt from processed feedback
   */
  private generateRegenerationPrompt(processedFeedback: ProcessedFeedback, originalContent: string): string {
    if (processedFeedback.regenerationPrompt) {
      return processedFeedback.regenerationPrompt;
    }

    const { analysis } = processedFeedback;
    let prompt = `Please improve the following content based on this feedback:\n\n`;
    prompt += `Feedback: "${processedFeedback.originalFeedback}"\n\n`;

    if (analysis.categories.length > 0) {
      prompt += `Focus areas:\n`;
      analysis.categories.forEach(category => {
        prompt += `- ${category.type}: ${category.description} (${category.severity})\n`;
      });
      prompt += `\n`;
    }

    if (analysis.improvementAreas.length > 0) {
      prompt += `Specific improvements needed:\n`;
      analysis.improvementAreas.forEach(area => {
        prompt += `- ${area.area}: ${area.description}\n`;
        if (area.suggestions.length > 0) {
          area.suggestions.forEach(suggestion => {
            prompt += `  * ${suggestion}\n`;
          });
        }
      });
      prompt += `\n`;
    }

    if (analysis.regenerationStrategy) {
      const strategy = analysis.regenerationStrategy;
      prompt += `Regeneration approach: ${strategy.approach.replace('_', ' ')}\n`;

      if (strategy.focusAreas.length > 0) {
        prompt += `Focus on: ${strategy.focusAreas.join(', ')}\n`;
      }

      if (strategy.preserveElements.length > 0) {
        prompt += `Preserve: ${strategy.preserveElements.join(', ')}\n`;
      }

      if (strategy.additionalInstructions.length > 0) {
        prompt += `Additional instructions:\n`;
        strategy.additionalInstructions.forEach(instruction => {
          prompt += `- ${instruction}\n`;
        });
      }
      prompt += `\n`;
    }

    prompt += `Original Content:\n${originalContent}\n\n`;
    prompt += `Please provide the improved version that addresses all the feedback points:`;

    return prompt;
  }

  /**
   * Regenerate content using AI with feedback
   */
  private async regenerateContentWithFeedback(
    originalContent: string,
    regenerationPrompt: string,
    analysis: any
  ): Promise<string> {
    console.log(`🤖 Regenerating content with AI feedback processing...`);

    try {
      // Use the AI model manager to regenerate content
      const aiResponse = await this.aiModelManager.generateContent({
        prompt: regenerationPrompt,
        maxTokens: 2000,
        temperature: 0.7,
        systemPrompt: `You are an expert content writer. Your task is to improve content based on specific feedback.
        Focus on addressing the exact issues mentioned in the feedback while maintaining the overall quality and structure of the content.
        Ensure the improved content is engaging, well-structured, and meets the specified requirements.`
      });

      if (aiResponse && aiResponse.content) {
        console.log(`✅ Content regenerated successfully (${aiResponse.content.length} characters)`);
        return aiResponse.content;
      } else {
        console.log(`⚠️ AI regeneration returned empty content`);
        return '';
      }

    } catch (error) {
      console.error(`❌ AI content regeneration failed:`, error);

      // Fallback: try simple content improvement
      return this.fallbackContentImprovement(originalContent, analysis);
    }
  }

  /**
   * Fallback content improvement when AI regeneration fails
   */
  private fallbackContentImprovement(originalContent: string, analysis: any): string {
    console.log(`🔄 Applying fallback content improvements...`);

    let improvedContent = originalContent;

    // Apply basic improvements based on analysis
    if (analysis.categories) {
      analysis.categories.forEach((category: any) => {
        switch (category.type) {
          case 'structure':
            // Add basic structure improvements
            if (!improvedContent.includes('# ') && !improvedContent.includes('## ')) {
              const lines = improvedContent.split('\n');
              if (lines.length > 3) {
                lines[0] = `# ${lines[0]}`;
                improvedContent = lines.join('\n');
              }
            }
            break;
          case 'content':
            // Add a note about content improvement
            improvedContent += '\n\n*Note: This content has been reviewed and improved based on feedback.*';
            break;
        }
      });
    }

    return improvedContent;
  }

  // Artifact management
  async createArtifact(executionId: string, stepId: string, artifact: Omit<WorkflowArtifact, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const artifactId = uuidv4();
    const now = new Date().toISOString();

    const newArtifact: WorkflowArtifact = {
      ...artifact,
      id: artifactId,
      createdAt: now,
      updatedAt: now
    };

    // Store artifact as content item
    const contentItem: ContentItem = {
      id: artifactId,
      type: this.mapArtifactTypeToContentType(artifact.type),
      title: artifact.title,
      content: artifact.content,
      status: artifact.status === ArtifactStatus.APPROVED ? ContentStatus.APPROVED : ContentStatus.PENDING,
      executionId,
      stepId,
      createdAt: now,
      updatedAt: now,
      metadata: {
        stepId,
        executionId,
        version: artifact.version,
        createdBy: artifact.createdBy,
        artifactType: artifact.type
      }
    };

    await this.stateStore.setContent(contentItem);

    // Update step result with artifact ID
    const execution = await this.getExecution(executionId);
    if (execution && execution.stepResults[stepId]) {
      execution.stepResults[stepId].artifactId = artifactId;
      await this.stateStore.setExecution(execution);
    }

    return artifactId;
  }

  async getArtifact(artifactId: string): Promise<WorkflowArtifact | null> {
    const content = await this.stateStore.getContent(artifactId);
    if (!content || !content.metadata) {
      return null;
    }

    return {
      id: content.id,
      stepId: content.metadata.stepId || content.stepId,
      executionId: content.metadata.executionId || content.executionId,
      type: content.metadata.artifactType as ArtifactType || ArtifactType.CONTENT_DRAFT,
      title: content.title,
      content: content.content,
      status: content.status === ContentStatus.APPROVED ? ArtifactStatus.APPROVED : ArtifactStatus.PENDING_APPROVAL,
      version: content.metadata.version || 1,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
      createdBy: content.metadata.createdBy || 'system',
      approvedBy: content.metadata.approvedBy,
      approvedAt: content.metadata.approvedAt,
      rejectedBy: content.metadata.rejectedBy,
      rejectedAt: content.metadata.rejectedAt,
      rejectionReason: content.metadata.rejectionReason
    };
  }

  async updateArtifact(artifactId: string, updates: Partial<WorkflowArtifact>): Promise<void> {
    const content = await this.stateStore.getContent(artifactId);
    if (!content) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    const updatedContent: ContentItem = {
      ...content,
      title: updates.title || content.title,
      content: updates.content || content.content,
      status: updates.status === ArtifactStatus.APPROVED ? ContentStatus.APPROVED : content.status,
      updatedAt: new Date().toISOString(),
      metadata: {
        ...content.metadata,
        version: updates.version || content.metadata?.version || 1,
        approvedBy: updates.approvedBy,
        approvedAt: updates.approvedAt,
        rejectedBy: updates.rejectedBy,
        rejectedAt: updates.rejectedAt,
        rejectionReason: updates.rejectionReason
      }
    };

    await this.stateStore.setContent(updatedContent);
  }

  // Store access
  getStore(): ISimplifiedStateStore {
    return this.stateStore;
  }

  // Approval handling
  async submitApproval(artifactId: string, decision: ApprovalDecision): Promise<void> {
    const artifact = await this.getArtifact(artifactId);
    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // Update artifact status based on decision
    const updates: Partial<WorkflowArtifact> = {
      status: decision.approved ? ArtifactStatus.APPROVED : ArtifactStatus.REJECTED,
      updatedAt: decision.timestamp
    };

    if (decision.approved) {
      updates.approvedBy = decision.approver;
      updates.approvedAt = decision.timestamp;
    } else {
      updates.rejectedBy = decision.approver;
      updates.rejectedAt = decision.timestamp;
      updates.rejectionReason = decision.reason;
    }

    await this.updateArtifact(artifactId, updates);

    // If approved, continue workflow execution
    if (decision.approved) {
      console.log(`✅ Artifact ${artifactId} approved - resuming workflow execution ${artifact.executionId}`);

      // Update step status to approved
      const execution = await this.getExecution(artifact.executionId);
      if (execution && execution.stepResults[artifact.stepId]) {
        console.log(`📝 Updating step ${artifact.stepId} status to APPROVED`);
        execution.stepResults[artifact.stepId].status = StepStatus.APPROVED;
        execution.stepResults[artifact.stepId].approvedBy = decision.approver;
        execution.stepResults[artifact.stepId].approvedAt = decision.timestamp;
        execution.stepResults[artifact.stepId].completedAt = decision.timestamp;

        // Resume execution status to RUNNING
        execution.status = ExecutionStatus.RUNNING;
        await this.stateStore.setExecution(execution);
        console.log(`💾 Execution ${artifact.executionId} updated with approved step and status set to RUNNING`);

        // Resume workflow execution
        console.log(`🚀 Resuming workflow execution ${artifact.executionId}...`);
        this.executeWorkflowSteps(artifact.executionId).catch(error => {
          console.error(`Workflow execution ${artifact.executionId} failed after approval:`, error);
          this.markExecutionFailed(artifact.executionId, error);
        });
      } else {
        console.log(`⚠️ Could not find execution ${artifact.executionId} or step ${artifact.stepId}`);
      }
    } else {
      // Handle rejection with feedback processing
      await this.handleRejectionWithFeedback(artifact, decision);
    }
  }

  /**
   * Handle artifact rejection with feedback and regeneration
   */
  async handleRejectionWithFeedback(artifact: WorkflowArtifact, decision: ApprovalDecision): Promise<void> {
    console.log(`❌ Artifact ${artifact.id} rejected with feedback: ${decision.reason}`);

    // Check if feedback is provided and actionable
    if (!decision.reason || decision.reason.trim().length < 10) {
      console.log(`⚠️ No actionable feedback provided, marking step as rejected without regeneration`);
      await this.markStepRejected(artifact, decision);
      return;
    }

    try {
      console.log(`🔄 Starting regeneration for artifact ${artifact.id} based on feedback`);

      // Regenerate artifact content using AI
      const regeneratedContent = await this.regenerateArtifactContent(artifact, decision.reason);

      // Create new artifact with regenerated content
      const newArtifactId = await this.createArtifact(artifact.executionId, artifact.stepId, {
        stepId: artifact.stepId,
        executionId: artifact.executionId,
        type: artifact.type,
        title: `${artifact.title} (Improved v${(artifact.version || 1) + 1})`,
        content: regeneratedContent,
        status: ArtifactStatus.PENDING_APPROVAL,
        version: (artifact.version || 1) + 1,
        createdBy: 'regeneration-system'
      });

      console.log(`✅ Regeneration successful: ${artifact.id} -> ${newArtifactId}`);

      // Update the step to point to the new artifact
      await this.updateStepWithRegeneratedArtifact(artifact, newArtifactId);

      // The new artifact will need approval again, so workflow stays paused
      console.log(`⏸️ Workflow paused for approval of regenerated artifact ${newArtifactId}`);

    } catch (error) {
      console.error(`❌ Error during regeneration process:`, error);
      await this.markStepRejected(artifact, decision);
    }
  }

  /**
   * Regenerate artifact content using AI based on feedback
   */
  private async regenerateArtifactContent(artifact: WorkflowArtifact, feedback: string): Promise<any> {
    console.log(`🤖 Regenerating content for artifact ${artifact.id} with feedback: ${feedback}`);

    // Build improvement prompt
    const systemPrompt = `You are an expert content improvement AI. Your task is to regenerate and improve content based on human feedback.

Guidelines:
1. Address all feedback points specifically and thoroughly
2. Maintain the original intent and core message
3. Improve quality while preserving the content's purpose
4. Ensure the output is better than the original in the areas mentioned
5. Keep the same format and structure unless feedback suggests otherwise

Focus on making targeted improvements rather than complete rewrites unless explicitly requested.`;

    const userPrompt = `Please improve the following content based on the feedback provided:

ORIGINAL CONTENT:
${JSON.stringify(artifact.content, null, 2)}

FEEDBACK:
${feedback}

Please provide the improved content in the same format as the original, focusing on the specific feedback provided.`;

    try {
      // Generate improved content using AI
      const result = await this.aiManager.generate(userPrompt, {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000,
        systemPrompt
      });

      // Try to parse as JSON if original was JSON
      let improvedContent = result.content;
      if (typeof artifact.content === 'object') {
        try {
          improvedContent = JSON.parse(result.content);
        } catch {
          // If parsing fails, wrap in appropriate structure
          improvedContent = {
            content: result.content,
            improved: true,
            feedback_addressed: feedback
          };
        }
      }

      console.log(`✅ Content regenerated successfully for artifact ${artifact.id}`);
      return improvedContent;

    } catch (error) {
      console.error(`❌ AI regeneration failed:`, error);
      throw new Error(`Failed to regenerate content: ${error.message}`);
    }
  }

  /**
   * Mark step as rejected without regeneration
   */
  private async markStepRejected(artifact: WorkflowArtifact, decision: ApprovalDecision): Promise<void> {
    const execution = await this.getExecution(artifact.executionId);
    if (execution && execution.stepResults[artifact.stepId]) {
      execution.stepResults[artifact.stepId].status = StepStatus.REJECTED;
      execution.stepResults[artifact.stepId].rejectionReason = decision.reason;
      execution.stepResults[artifact.stepId].error = `Artifact rejected: ${decision.reason}`;

      await this.stateStore.setExecution(execution);
      console.log(`📝 Step ${artifact.stepId} marked as rejected`);
    }
  }

  /**
   * Update step to point to regenerated artifact
   */
  private async updateStepWithRegeneratedArtifact(originalArtifact: WorkflowArtifact, newArtifactId: string): Promise<void> {
    const execution = await this.getExecution(originalArtifact.executionId);
    if (execution && execution.stepResults[originalArtifact.stepId]) {
      // Update step result with new artifact ID
      execution.stepResults[originalArtifact.stepId].artifactId = newArtifactId;
      execution.stepResults[originalArtifact.stepId].status = StepStatus.WAITING_APPROVAL;
      execution.stepResults[originalArtifact.stepId].outputs = {
        ...execution.stepResults[originalArtifact.stepId].outputs,
        artifact_id: newArtifactId,
        regenerated_from: originalArtifact.id,
        regeneration_timestamp: new Date().toISOString()
      };

      await this.stateStore.setExecution(execution);
      console.log(`📝 Step ${originalArtifact.stepId} updated with regenerated artifact ${newArtifactId}`);
    }
  }

  async getApprovalStatus(artifactId: string): Promise<ApprovalStatus> {
    const artifact = await this.getArtifact(artifactId);
    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // For now, return a simple approval status
    // In a full implementation, this would track multiple approvers
    return {
      artifactId,
      status: artifact.status,
      approvals: artifact.approvedBy ? [{
        approved: true,
        approver: artifact.approvedBy,
        timestamp: artifact.approvedAt || new Date().toISOString(),
        feedback: ''
      }] : [],
      requiredApprovals: 1,
      pendingApprovers: artifact.status === ArtifactStatus.PENDING_APPROVAL ? userManager.getDefaultApprovers() : [],
      canProceed: artifact.status === ArtifactStatus.APPROVED,
      escalated: false,
      escalationLevel: 0
    };
  }

  // Private methods for workflow execution
  private async executeWorkflowSteps(executionId: string): Promise<void> {
    // Create recovery checkpoint before starting
    try {
      const execution = await this.getExecution(executionId);
      if (execution) {
        await this.recoveryManager.createCheckpoint(
          executionId,
          execution.currentStep || 'start',
          execution
        );
      }
    } catch (error) {
      console.warn(`⚠️ Failed to create recovery checkpoint: ${error.message}`);
    }

    // Execute with comprehensive error handling
    return await this.errorHandler.executeWithRetry(
      async () => {
        console.log(`🚀 Starting executeWorkflowSteps for execution ${executionId}`);

        console.log(`🔍 Step 1: Getting execution ${executionId}`);
        const execution = await this.getExecution(executionId);
        if (!execution) {
          console.log(`❌ Execution ${executionId} not found`);
          throw new Error(`Execution ${executionId} not found`);
        }

        return await this.executeWorkflowStepsInternal(execution);
      },
      {
        operationName: 'executeWorkflowSteps',
        executionId,
        errorType: ErrorType.WORKFLOW_EXECUTION
      }
    );
  }

  /**
   * Internal workflow steps execution with error handling
   */
  private async executeWorkflowStepsInternal(execution: WorkflowExecution): Promise<void> {
    try {

      console.log(`📋 Found execution ${execution.id}, status: ${execution.status}, progress: ${execution.progress}%`);

      console.log(`🔍 Step 2: Checking execution status`);
      if (execution.status !== ExecutionStatus.RUNNING) {
        console.log(`⏸️ Execution ${execution.id} is not running (status: ${execution.status}), skipping execution`);
        return;
      }

      console.log(`🔍 Step 3: Getting workflow ${execution.workflowId}`);
      const workflow = await this.getWorkflow(execution.workflowId);
      if (!workflow) {
        console.log(`❌ Workflow ${execution.workflowId} not found`);
        throw new Error(`Workflow ${execution.workflowId} not found`);
      }

      console.log(`📋 Found workflow ${execution.workflowId}: ${workflow.name}`);
      console.log(`📋 Current step: ${execution.currentStep || 'none'}`);
      console.log(`📋 Step results:`, Object.keys(execution.stepResults).map(stepId => ({
        stepId,
        status: execution.stepResults[stepId].status
      })));

      // Find next step to execute - refresh execution state first to ensure we have latest data
      console.log(`🔍 Step 4: Looking for next step to execute...`);
      console.log(`🔍 Step 4.1: Refreshing execution state to ensure latest data...`);
      const refreshedExecution = await this.getExecution(executionId);
      if (!refreshedExecution) {
        console.log(`❌ Failed to refresh execution ${executionId}`);
        return;
      }

      const nextStep = await this.findNextStep(workflow, refreshedExecution);
      console.log(`🔍 Step 5: findNextStep returned:`, nextStep ? `${nextStep.id} (${nextStep.name})` : 'null');

      if (!nextStep) {
        // Check if we're waiting for any approvals
        const waitingSteps = Object.values(refreshedExecution.stepResults).filter(result =>
          result.status === StepStatus.WAITING_APPROVAL || result.status === StepStatus.WAITING_REVIEW
        );

        if (waitingSteps.length > 0) {
          // Workflow is paused waiting for approval
          refreshedExecution.status = ExecutionStatus.PAUSED;
          await this.stateStore.setExecution(refreshedExecution);
          console.log(`⏸️ Workflow ${refreshedExecution.id} paused - waiting for ${waitingSteps.length} approval(s)`);
          return;
        }

        // All steps completed
        await this.markExecutionCompleted(executionId);
        return;
      }

      // Check if step is waiting for review or approval
      console.log(`🔍 Checking if step ${nextStep.id} is waiting for review/approval...`);
      const stepResult = refreshedExecution.stepResults[nextStep.id];
      console.log(`🔍 Step result for ${nextStep.id}:`, stepResult ? {
        status: stepResult.status,
        hasOutputs: !!stepResult.outputs,
        startedAt: stepResult.startedAt
      } : 'null');

      if (stepResult && (
        stepResult.status === StepStatus.WAITING_REVIEW ||
        stepResult.status === StepStatus.WAITING_APPROVAL
      )) {
        console.log(`⏸️ Step ${nextStep.id} is waiting for review/approval, skipping execution`);
        return; // Wait for review or approval
      }

      console.log(`✅ Step ${nextStep.id} is ready for execution`);



      // Execute the step with enhanced error handling
      const stepResult = await this.executeStepWithErrorHandling(refreshedExecution, nextStep);

      // Check if workflow should pause after this step
      if (stepResult.status === StepStatus.WAITING_APPROVAL ||
          stepResult.status === StepStatus.WAITING_REVIEW) {
        console.log(`⏸️ Workflow ${refreshedExecution.id} paused after step ${nextStep.id} - status: ${stepResult.status}`);

        // Update execution status to paused
        const updatedExecution = await this.getExecution(refreshedExecution.id);
        if (updatedExecution) {
          updatedExecution.status = ExecutionStatus.PAUSED;
          await this.stateStore.setExecution(updatedExecution);
        }
        return; // Stop execution here
      }

      // Create recovery checkpoint after successful step
      await this.recoveryManager.createCheckpoint(
        refreshedExecution.id,
        nextStep.id,
        refreshedExecution,
        { stepResult }
      );

      // Emit step completion event to continue workflow execution
      await this.eventBus.emit(
        WorkflowEventType.STEP_COMPLETED,
        {
          stepId: nextStep.id,
          stepResult,
          outputs: stepResult.outputs
        },
        'workflow-engine',
        {
          executionId: refreshedExecution.id,
          stepId: nextStep.id
        }
      );
    } catch (error) {
      console.error(`❌ Critical error in executeWorkflowStepsInternal for ${execution.id}:`, error);

      // Handle error with recovery system
      const recoveryResult = await this.errorHandler.handleError(error as Error, {
        executionId: execution.id,
        type: ErrorType.WORKFLOW_EXECUTION,
        severity: ErrorSeverity.HIGH,
        additionalContext: {
          currentStep: execution.currentStep,
          progress: execution.progress
        }
      });

      if (recoveryResult.success) {
        console.log(`🔧 Recovery successful: ${recoveryResult.message}`);

        if (recoveryResult.nextAction === 'retry_operation') {
          // Retry will be handled by the outer executeWithRetry
          throw error;
        } else if (recoveryResult.nextAction === 'continue_workflow') {
          // Continue with next step
          return;
        }
      } else {
        console.error(`❌ Recovery failed: ${recoveryResult.message}`);
        await this.markExecutionFailed(execution.id, error as Error);
      }

      throw error;
    }
  }

  /**
   * Execute a single step with comprehensive error handling
   */
  private async executeStepWithErrorHandling(
    execution: WorkflowExecution,
    step: WorkflowStep
  ): Promise<StepResult> {
    console.log(`🚀 Executing step ${step.id} (${step.name}) with error handling`);

    return await this.errorHandler.executeWithRetry(
      async () => {
        const stepResult = await this.executeWorkflowStep(execution, step);

        console.log(`✅ Step ${step.id} completed successfully:`, {
          stepId: stepResult.stepId,
          status: stepResult.status,
          hasOutputs: !!stepResult.outputs,
          outputKeys: stepResult.outputs ? Object.keys(stepResult.outputs) : []
        });

        return stepResult;
      },
      {
        operationName: `executeStep-${step.id}`,
        executionId: execution.id,
        stepId: step.id,
        errorType: ErrorType.STEP_EXECUTION
      }
    );
  }

  private async findNextStep(workflow: Workflow, execution: WorkflowExecution): Promise<WorkflowStep | null> {
    console.log(`🔍 Finding next step for execution ${execution.id}`);
    console.log(`📋 Available steps: ${workflow.steps.map(s => s.id).join(', ')}`);

    for (const step of workflow.steps) {
      const stepResult = execution.stepResults[step.id];

      console.log(`🔍 Checking step ${step.id}:`, {
        hasResult: !!stepResult,
        status: stepResult?.status,
        dependencies: step.dependencies,
        stepResult: stepResult ? {
          stepId: stepResult.stepId,
          status: stepResult.status,
          completedAt: stepResult.completedAt,
          outputs: Object.keys(stepResult.outputs || {})
        } : null
      });

      // Skip steps that don't need execution
      if (stepResult) {
        if (stepResult.status === StepStatus.COMPLETED ||
            stepResult.status === StepStatus.APPROVED ||
            stepResult.status === StepStatus.WAITING_REVIEW ||
            stepResult.status === StepStatus.WAITING_APPROVAL ||
            stepResult.status === StepStatus.FAILED) {
          console.log(`⏭️ Skipping step ${step.id} - status: ${stepResult.status}`);
          continue;
        }
      }

      // Check dependencies - steps must be completed or approved
      const dependenciesMet = await this.checkDependenciesMet(step.dependencies, execution);

      console.log(`🔍 Checking dependencies for step ${step.id}:`, {
        dependencies: step.dependencies,
        dependenciesMet,
        stepType: step.type
      });

      if (dependenciesMet) {
        console.log(`✅ Dependencies met for step ${step.id} - this is the next step!`);

        // Check step condition if it exists
        if (step.condition && !this.evaluateStepCondition(step.condition, execution)) {
          // Mark step as skipped
          this.markStepSkipped(execution.id, step.id, 'condition_not_met');
          continue;
        }

        return step;
      } else {
        console.log(`❌ Dependencies NOT met for step ${step.id}`);
      }
    }

    console.log(`❌ No next step found for execution ${execution.id}`);
    return null;
  }

  /**
   * Check if step dependencies are met, including approval gate requirements
   */
  private async checkDependenciesMet(dependencies: string[], execution: WorkflowExecution): Promise<boolean> {
    for (const depId of dependencies) {
      const depResult = execution.stepResults[depId];

      if (!depResult) {
        console.log(`❌ Dependency ${depId} not found`);
        return false;
      }

      // For approval gates, check if artifact is actually approved
      if (depResult.stepType === 'approval_gate' || depResult.approvalRequired) {
        if (depResult.artifactId) {
          try {
            const artifact = await this.getArtifact(depResult.artifactId);
            if (!artifact || artifact.status !== ArtifactStatus.APPROVED) {
              console.log(`⏸️ Approval gate ${depId} not approved yet (artifact: ${depResult.artifactId}, status: ${artifact?.status})`);
              return false;
            }
            console.log(`✅ Approval gate ${depId} is approved`);
          } catch (error) {
            console.log(`❌ Error checking artifact for ${depId}:`, error);
            return false;
          }
        } else {
          console.log(`❌ Approval gate ${depId} missing artifact ID`);
          return false;
        }
      } else {
        // Regular steps just need to be completed
        if (depResult.status !== StepStatus.COMPLETED && depResult.status !== StepStatus.APPROVED) {
          console.log(`❌ Regular step ${depId} not completed (status: ${depResult.status})`);
          return false;
        }
        console.log(`✅ Regular step ${depId} is completed`);
      }
    }

    return true;
  }

  // Evaluate step condition
  private evaluateStepCondition(condition: StepCondition, execution: WorkflowExecution): boolean {
    const results = condition.rules.map(rule => this.evaluateConditionalRule(rule, execution));

    if (condition.logic === 'AND') {
      return results.every(result => result);
    } else {
      return results.some(result => result);
    }
  }

  // Evaluate a single conditional rule
  private evaluateConditionalRule(rule: ConditionalRule, execution: WorkflowExecution): boolean {
    // Get variable value from execution context
    const variableValue = this.getVariableValue(rule.variable, execution);

    switch (rule.operator) {
      case 'equals':
        return variableValue === rule.value;
      case 'not_equals':
        return variableValue !== rule.value;
      case 'contains':
        return String(variableValue).includes(String(rule.value));
      case 'not_contains':
        return !String(variableValue).includes(String(rule.value));
      case 'greater_than':
        return Number(variableValue) > Number(rule.value);
      case 'less_than':
        return Number(variableValue) < Number(rule.value);
      case 'exists':
        return variableValue !== undefined && variableValue !== null;
      case 'not_exists':
        return variableValue === undefined || variableValue === null;
      default:
        return false;
    }
  }

  // Get variable value from execution context
  private getVariableValue(variableName: string, execution: WorkflowExecution): any {
    // Check inputs first
    if (execution.inputs[variableName] !== undefined) {
      return execution.inputs[variableName];
    }

    // Check step outputs
    for (const stepResult of Object.values(execution.stepResults)) {
      if (stepResult.outputs[variableName] !== undefined) {
        return stepResult.outputs[variableName];
      }
    }

    // Check execution outputs
    if (execution.outputs[variableName] !== undefined) {
      return execution.outputs[variableName];
    }

    return undefined;
  }

  // Mark step as skipped
  private async markStepSkipped(executionId: string, stepId: string, reason: string): Promise<void> {
    const stepResult: StepResult = {
      stepId,
      status: StepStatus.SKIPPED,
      inputs: {},
      outputs: {},
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      duration: 0,
      metadata: { reason }
    };

    await this.updateStepResult(executionId, stepId, stepResult);
  }

  private async executeWorkflowStep(execution: WorkflowExecution, step: WorkflowStep): Promise<StepResult> {
    console.log(`🚨 EXECUTEWORKFLOWSTEP CALLED FOR ${step.id}`);
    const startTime = new Date().toISOString();

    // Create initial step result
    console.log(`🔍 CRITICAL: About to call buildStepInputs for ${step.id}`);
    const stepInputs = this.buildStepInputs(execution, step);
    console.log(`🔍 CRITICAL: buildStepInputs completed for ${step.id}, inputs:`, stepInputs);
    console.log(`🔍 CRITICAL: Creating step result object for ${step.id}...`);

    const stepResult: StepResult = {
      stepId: step.id,
      status: StepStatus.RUNNING,
      inputs: stepInputs,
      outputs: {},
      startedAt: startTime,
      stepType: step.type,
      approvalRequired: step.type === StepType.APPROVAL_GATE
    };

    console.log(`🔍 CRITICAL: Step result created for ${step.id}:`, {
      stepId: stepResult.stepId,
      status: stepResult.status,
      hasInputs: !!stepResult.inputs,
      stepType: stepResult.stepType
    });

    // Update execution with running step
    await this.updateStepResult(execution.id, step.id, stepResult);

    // Emit step started event
    await this.eventBus.emit(
      WorkflowEventType.STEP_STARTED,
      {
        stepId: step.id,
        stepName: step.name,
        stepType: step.type,
        inputs: stepResult.inputs
      },
      'workflow-engine',
      {
        executionId: execution.id,
        stepId: step.id
      }
    );

    // Broadcast real-time step started event
    this.realTimeBroadcaster.broadcastStepStarted(execution.id, step.id, step.name, {
      stepType: step.type,
      inputs: stepResult.inputs
    });

    try {
      let outputs: Record<string, any> = {};

      // Debug logging
      console.log(`🔧 Executing step ${step.id} of type ${step.type} in execution ${execution.id}`);

      switch (step.type) {
        case StepType.TEXT_INPUT:
          outputs = await this.executeTextInput(step, stepResult.inputs, execution.id);
          break;
        case StepType.AI_GENERATION:
          // Check if this is a results compilation step
          if (step.id === 'results-compilation' || step.name.toLowerCase().includes('results compilation')) {
            console.log(`📊 Executing results compilation for step ${step.id}`);
            outputs = await this.executeResultsCompilation(execution, step, stepResult.inputs);
          } else {
            outputs = await this.executeAIGeneration(step, stepResult.inputs, execution.id);
          }
          break;
        case StepType.HUMAN_REVIEW:
          console.log(`📝 Creating human review for step ${step.id}`);
          outputs = await this.executeHumanReview(execution, step, stepResult.inputs);
          break;
        case StepType.APPROVAL_GATE:
          console.log(`✅ Creating approval gate for step ${step.id}`);
          outputs = await this.executeApprovalGate(execution, step, stepResult.inputs);
          break;
        case StepType.SEO_OPTIMIZATION:
          console.log(`🔍 Executing SEO optimization for step ${step.id}`);
          outputs = await this.executeSEOOptimization(execution, step, stepResult.inputs);
          break;
        case StepType.CMS_INTEGRATION:
          console.log(`📝 Executing CMS integration for step ${step.id}`);
          outputs = await this.executeCMSIntegration(execution, step, stepResult.inputs);
          break;
        case StepType.COMPLETION:
          console.log(`🎉 Executing workflow completion for step ${step.id}`);
          outputs = await this.executeCompletion(execution, step, stepResult.inputs);
          break;
        default:
          throw new Error(`Step type ${step.type} not implemented`);
      }

      // For approval gates and human review, don't mark as completed - they should stay in WAITING_APPROVAL/WAITING_REVIEW
      if (step.type === StepType.APPROVAL_GATE) {
        console.log(`⏸️ Approval gate ${step.id} created, staying in WAITING_APPROVAL status`);

        // Update step result to waiting for approval
        const waitingResult: StepResult = {
          ...stepResult,
          status: StepStatus.WAITING_APPROVAL,
          outputs,
          artifactId: outputs.artifact_id,
          approvalRequired: true,
          completedAt: undefined // Not completed yet
        };

        await this.updateStepResult(execution.id, step.id, waitingResult);
        return waitingResult;
      }

      // For human review steps, pause the workflow until review is completed
      if (step.type === StepType.HUMAN_REVIEW) {
        console.log(`⏸️ Human review ${step.id} created, staying in WAITING_REVIEW status`);

        // Update step result to waiting for review
        const waitingResult: StepResult = {
          ...stepResult,
          status: StepStatus.WAITING_REVIEW,
          outputs,
          artifactId: outputs.review_id,
          approvalRequired: true,
          completedAt: undefined // Not completed yet
        };

        await this.updateStepResult(execution.id, step.id, waitingResult);
        return waitingResult;
      }

      // Mark regular steps as completed
      const completedResult: StepResult = {
        ...stepResult,
        status: StepStatus.COMPLETED,
        outputs,
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, completedResult);

      // Broadcast real-time step completed event
      this.realTimeBroadcaster.broadcastStepCompleted(execution.id, step.id, step.name, outputs, {
        duration: completedResult.duration,
        stepType: step.type
      });

      return completedResult;

    } catch (error) {
      const failedResult: StepResult = {
        ...stepResult,
        status: StepStatus.FAILED,
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, failedResult);

      // Broadcast real-time step failed event
      this.realTimeBroadcaster.broadcastStepFailed(execution.id, step.id, step.name, error, {
        duration: failedResult.duration,
        stepType: step.type
      });

      throw error;
    }
  }

  private buildStepInputs(execution: WorkflowExecution, step: WorkflowStep): Record<string, any> {
    console.log(`🔧 Building inputs for step ${step.id} (${step.name})`);
    console.log(`📋 Step expects inputs:`, step.inputs);

    const context: VariableContext = {
      ...execution.inputs
    };

    // Add outputs from previous steps
    for (const [stepId, result] of Object.entries(execution.stepResults)) {
      if (result.status === StepStatus.COMPLETED) {
        console.log(`📋 Adding outputs from completed step ${stepId}:`, Object.keys(result.outputs));
        Object.assign(context, result.outputs);
      }
    }

    console.log(`📋 Available context keys:`, Object.keys(context));

    // Extract only the inputs this step needs
    const stepInputs: Record<string, any> = {};
    for (const inputName of step.inputs) {
      if (context[inputName] !== undefined) {
        stepInputs[inputName] = context[inputName];
        console.log(`✅ Found input ${inputName} for step ${step.id}`);
      } else {
        console.log(`❌ Missing input ${inputName} for step ${step.id}`);
      }
    }

    console.log(`🔧 Final inputs for step ${step.id}:`, Object.keys(stepInputs));
    return stepInputs;
  }

  private async executeTextInput(step: WorkflowStep, inputs: Record<string, any>, executionId: string): Promise<Record<string, any>> {
    console.log(`📝 Executing TEXT_INPUT step ${step.id}`);
    console.log(`📝 Step outputs expected:`, step.outputs);
    console.log(`📝 Available inputs:`, Object.keys(inputs));

    // For text input steps, we need to get the values from the execution context
    // since the user inputs are stored there, not in the step inputs
    const execution = await this.stateStore.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const outputs: Record<string, any> = {};

    for (const outputName of step.outputs) {
      // First try to get from step inputs (if provided)
      if (inputs[outputName] !== undefined) {
        outputs[outputName] = inputs[outputName];
        console.log(`✅ Mapped from inputs ${outputName}: ${inputs[outputName]}`);
      }
      // Then try to get from execution inputs (user-provided data)
      else if (execution.inputs && execution.inputs[outputName] !== undefined) {
        outputs[outputName] = execution.inputs[outputName];
        console.log(`✅ Mapped from execution ${outputName}: ${execution.inputs[outputName]}`);
      } else {
        console.log(`⚠️ Missing input for output ${outputName}`);
      }
    }

    console.log(`📝 TEXT_INPUT step ${step.id} completed with outputs:`, Object.keys(outputs));
    return outputs;
  }

  private async executeAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId?: string): Promise<Record<string, any>> {
    // Check if agent consultation is enabled for this step
    if (step.consultationConfig?.enabled) {
      console.log(`🤖 Using enhanced AI generation with agent consultation for step ${step.id}`);
      return await this.executeEnhancedAIGeneration(step, inputs, executionId || '');
    }

    // Fall back to regular AI generation
    console.log(`🤖 Using regular AI generation for step ${step.id}`);
    return await this.executeRegularAIGeneration(step, inputs, executionId);
  }

  private async executeEnhancedAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId: string): Promise<Record<string, any>> {
    try {
      console.log(`🚀 Executing enhanced AI generation with agent consultation for step ${step.id}`);

      // Use the enhanced AI generation step
      const result = await this.enhancedAIStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        executionId
      );

      // Create content item for the enhanced content
      const contentId = uuidv4();
      const contentItem: ContentItem = {
        id: contentId,
        type: this.inferContentType(step.type),
        title: step.name,
        content: result.outputs.content || result.outputs,
        status: ContentStatus.DRAFT,
        executionId,
        stepId: step.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          enhancedWithAgents: true,
          consultationSummary: result.outputs.consultationSummary,
          agentInsights: result.outputs.agentInsights,
          consultationResults: result.consultationResults?.length || 0
        }
      };

      await this.stateStore.setContent(contentItem);

      // Return enhanced outputs
      const outputs: Record<string, any> = {
        content_id: contentId,
        ...result.outputs
      };

      // Map to step outputs
      for (const outputName of step.outputs) {
        if (outputName === 'content' || outputName.includes('content')) {
          outputs[outputName] = result.outputs.content || result.outputs[outputName];
        } else if (outputName.includes('id')) {
          outputs[outputName] = contentId;
        }
      }

    console.log(`✅ Enhanced AI generation outputs for step ${step.id}:`, Object.keys(outputs));

      console.log(`✅ Enhanced AI generation completed for step ${step.id} with ${result.consultationResults?.length || 0} consultations`);
      return outputs;

    } catch (error) {
      console.error(`❌ Enhanced AI generation failed for step ${step.id}:`, error);

      // Fall back to regular AI generation if consultation fails and fallback is 'continue'
      if (step.consultationConfig?.fallbackBehavior === 'continue') {
        console.log(`🔄 Falling back to regular AI generation for step ${step.id}`);
        return await this.executeRegularAIGeneration(step, inputs, executionId);
      }

      throw error;
    }
  }

  private async executeRegularAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId?: string): Promise<Record<string, any>> {
    const aiConfig = step.config.aiConfig;
    if (!aiConfig) {
      throw new Error('AI configuration not found for AI generation step');
    }

    // Replace variables in prompt
    let prompt = aiConfig.prompt;
    for (const [key, value] of Object.entries(inputs)) {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Generate content using AI
    const result = await this.aiManager.generate(prompt, {
      provider: aiConfig.provider,
      model: aiConfig.model,
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens,
      systemPrompt: aiConfig.systemPrompt,
      userApiKey: aiConfig.userApiKey
    });

    // Create content item
    const contentId = uuidv4();
    const contentItem: ContentItem = {
      id: contentId,
      type: this.inferContentType(step.type),
      title: step.name,
      content: result.content,
      status: ContentStatus.DRAFT,
      executionId: executionId || '',
      stepId: step.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        aiModel: result.model,
        aiProvider: result.provider,
        cost: result.cost,
        wordCount: result.content.split(' ').length,
        enhancedWithAgents: false
      }
    };

    await this.stateStore.setContent(contentItem);

    // Return outputs
    const outputs: Record<string, any> = {
      content_id: contentId,
      content: result.content
    };

    // Map to step outputs
    for (const outputName of step.outputs) {
      if (outputName === 'content' || outputName.includes('content')) {
        outputs[outputName] = result.content;
      } else if (outputName.includes('id')) {
        outputs[outputName] = contentId;
      }
    }

    console.log(`✅ Regular AI generation outputs for step ${step.id}:`, Object.keys(outputs));
    console.log(`📋 Step expected outputs:`, step.outputs);
    console.log(`📋 Actual output keys:`, Object.keys(outputs));

    return outputs;
  }

  private async executeHumanReview(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`👥 Executing human review: ${step.name} (${step.id})`);
    console.log(`📋 Inputs:`, Object.keys(inputs));
    console.log(`📋 Input values:`, inputs);

    const reviewConfig = step.config.reviewConfig;
    if (!reviewConfig) {
      throw new Error('Review configuration not found for human review step');
    }

    // Find content to review
    let contentToReview = '';
    let contentId = '';
    let allContent = inputs;

    // First, try to find content in direct inputs
    for (const [key, value] of Object.entries(inputs)) {
      if (key.includes('content') && typeof value === 'string') {
        contentToReview = value;
      }
      // Handle nested content objects (e.g., blog_content.content)
      if (key.includes('content') && typeof value === 'object' && value !== null) {
        const contentObj = value as any;
        if (contentObj.content && typeof contentObj.content === 'string') {
          contentToReview = contentObj.content;
          console.log(`📋 Found nested content in ${key}.content`);
        }
      }
      if (key.includes('id') || key.includes('content_id')) {
        contentId = String(value);
      }
    }

    // If no content found in direct inputs, try to get content from dependent steps
    if (!contentToReview && step.dependencies.length > 0) {
      console.log(`📋 No direct content found, collecting content from dependencies: ${step.dependencies}`);
      for (const depStepId of step.dependencies) {
        const depResult = execution.stepResults[depStepId];
        if (depResult && depResult.outputs) {
          Object.assign(allContent, depResult.outputs);
          console.log(`📋 Added content from step ${depStepId}:`, Object.keys(depResult.outputs));

          // Try to find content in dependency outputs
          for (const [key, value] of Object.entries(depResult.outputs)) {
            if (key.includes('content') && typeof value === 'string' && !contentToReview) {
              contentToReview = value;
              console.log(`📋 Found content in dependency ${depStepId}: ${key}`);
            }
            // Handle nested content objects (e.g., blog_content.content, keyword_research.content)
            if (key.includes('content') && typeof value === 'object' && value !== null && !contentToReview) {
              const contentObj = value as any;
              if (contentObj.content && typeof contentObj.content === 'string') {
                contentToReview = contentObj.content;
                console.log(`📋 Found nested content in dependency ${depStepId}: ${key}.content`);
              }
            }
            if ((key.includes('id') || key.includes('content_id')) && !contentId) {
              contentId = String(value);
            }
          }
        }
      }
    }

    console.log(`🔍 Content to review found: ${contentToReview ? 'Yes' : 'No'} (length: ${contentToReview.length})`);
    console.log(`🔍 Content ID: ${contentId}`);

    if (!contentToReview) {
      console.error(`❌ No content found to review. Available data:`, allContent);
      throw new Error('No content found to review');
    }

    // Create review
    console.log(`🔍 Creating review with content length: ${contentToReview.length}`);
    const reviewLink = await this.reviewSystem.createReview(contentToReview, {
      contentId: contentId || uuidv4(),
      executionId: execution.id,
      stepId: step.id,
      type: reviewConfig.reviewType as any,
      instructions: reviewConfig.instructions,
      deadline: reviewConfig.deadline,
      reviewers: reviewConfig.reviewers
    });
    console.log(`📝 Review created with ID: ${reviewLink.reviewId}`);

    // Return review info (step will be completed when review is submitted)
    return {
      review_id: reviewLink.reviewId,
      review_url: reviewLink.url,
      status: 'waiting_review'
    };
  }

  private async executeApprovalGate(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`🚪 Executing approval gate: ${step.name} (${step.id})`);
    console.log(`📋 Inputs:`, Object.keys(inputs));
    console.log(`📋 Input values:`, inputs);

    // Determine artifact type based on step name or inputs
    let artifactType: ArtifactType = ArtifactType.CONTENT_DRAFT;

    if (step.name.toLowerCase().includes('keyword')) {
      artifactType = ArtifactType.KEYWORD_RESEARCH;
    } else if (step.name.toLowerCase().includes('content')) {
      artifactType = ArtifactType.CONTENT_DRAFT;
    } else if (step.name.toLowerCase().includes('seo')) {
      artifactType = ArtifactType.SEO_OPTIMIZATION;
    }

    console.log(`📄 Artifact type determined: ${artifactType}`);

    // Get content from previous steps for approval
    let contentToApprove = inputs;

    // If inputs are empty, try to get content from dependent steps
    if (Object.keys(inputs).length === 0 && step.dependencies.length > 0) {
      console.log(`📋 No direct inputs, collecting content from dependencies: ${step.dependencies}`);
      for (const depStepId of step.dependencies) {
        const depResult = execution.stepResults[depStepId];
        if (depResult && depResult.outputs) {
          Object.assign(contentToApprove, depResult.outputs);
          console.log(`📋 Added content from step ${depStepId}:`, Object.keys(depResult.outputs));
        }
      }
    }

    console.log(`🏗️ Creating artifact for approval gate with content:`, Object.keys(contentToApprove));
    const artifactId = await this.createArtifact(execution.id, step.id, {
      stepId: step.id,
      executionId: execution.id,
      type: artifactType,
      title: `${step.name} - Approval Required`,
      content: contentToApprove,
      status: ArtifactStatus.PENDING_APPROVAL,
      version: 1,
      createdBy: execution.metadata.userId || 'system'
    });
    console.log(`✅ Artifact created with ID: ${artifactId}`);

    console.log(`🎯 Approval gate created successfully. Workflow will pause until artifact ${artifactId} is approved.`);

    // Return the artifact ID and approval URL
    return {
      artifact_id: artifactId,
      approval_url: `/workflow/approval/${artifactId}`,
      status: 'waiting_approval',
      instructions: step.config.reviewConfig?.instructions || 'Please review and approve this artifact to continue the workflow.'
    };
  }

  private inferContentType(stepType: StepType): ContentType {
    switch (stepType) {
      case StepType.AI_GENERATION:
        return ContentType.GENERIC;
      case StepType.KEYWORD_RESEARCH:
        return ContentType.KEYWORD_RESEARCH;
      case StepType.CONTENT_CREATION:
        return ContentType.BLOG_POST;
      case StepType.SEO_OPTIMIZATION:
        return ContentType.SEO_ANALYSIS;
      default:
        return ContentType.GENERIC;
    }
  }

  private mapArtifactTypeToContentType(artifactType: ArtifactType): ContentType {
    switch (artifactType) {
      case ArtifactType.KEYWORD_RESEARCH:
        return ContentType.KEYWORD_RESEARCH;
      case ArtifactType.CONTENT_STRATEGY:
        return ContentType.GENERIC;
      case ArtifactType.CONTENT_DRAFT:
        return ContentType.BLOG_POST;
      case ArtifactType.SEO_OPTIMIZATION:
        return ContentType.SEO_ANALYSIS;
      case ArtifactType.FINAL_CONTENT:
        return ContentType.BLOG_POST;
      case ArtifactType.REVIEW_FEEDBACK:
        return ContentType.GENERIC;
      default:
        return ContentType.GENERIC;
    }
  }

  /**
   * Register an approval gate for a workflow
   */
  async registerApprovalGate(gate: ApprovalGate): Promise<void> {
    try {
      // Store approval gate in state
      await this.stateStore.update(state => {
        if (!state) return null;

        return {
          ...state,
          approvalGates: {
            ...state.approvalGates,
            [gate.id]: gate
          }
        };
      });

      console.log(`✅ Approval gate ${gate.id} registered for step ${gate.stepId}`);
    } catch (error) {
      console.error(`Failed to register approval gate ${gate.id}:`, error);
      throw error;
    }
  }

  private async updateStepResult(executionId: string, stepId: string, stepResult: StepResult): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: stepResult
      },
      currentStep: stepResult.status === StepStatus.RUNNING ? stepId : execution.currentStep,
      progress: this.calculateProgress(execution.stepResults, stepId, stepResult)
    };

    await this.stateStore.setExecution(updatedExecution);

    // Broadcast progress update
    this.realTimeBroadcaster.broadcastProgressUpdate(
      execution.id,
      updatedExecution.progress,
      updatedExecution.currentStep,
      {
        stepId,
        stepStatus: stepResult.status,
        totalSteps: Object.keys(updatedExecution.stepResults).length,
        completedSteps: Object.values(updatedExecution.stepResults).filter(r => r.status === StepStatus.COMPLETED).length
      }
    );
  }

  private calculateProgress(stepResults: Record<string, StepResult>, currentStepId: string, currentResult: StepResult): number {
    const allResults = { ...stepResults, [currentStepId]: currentResult };
    const totalSteps = Object.keys(allResults).length;
    const completedSteps = Object.values(allResults).filter(r => r.status === StepStatus.COMPLETED).length;

    return Math.round((completedSteps / totalSteps) * 100);
  }

  private async markExecutionCompleted(executionId: string): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.COMPLETED,
      progress: 100,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);

    // Emit workflow completed event
    await this.eventBus.emit(
      WorkflowEventType.WORKFLOW_COMPLETED,
      {
        executionId,
        workflowId: execution.workflowId,
        outputs: execution.outputs,
        duration: new Date().getTime() - new Date(execution.startedAt).getTime(),
        stepCount: Object.keys(execution.stepResults).length
      },
      'workflow-engine',
      { executionId }
    );

    // Broadcast real-time workflow completed event
    const duration = new Date().getTime() - new Date(execution.startedAt).getTime();
    this.realTimeBroadcaster.broadcastWorkflowCompleted(executionId, execution.workflowId, duration, {
      outputs: execution.outputs,
      stepCount: Object.keys(execution.stepResults).length,
      progress: 100
    });

    console.log(`🎉 Workflow execution ${executionId} completed successfully!`);
  }

  private async markExecutionFailed(executionId: string, error: any): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    console.log(`❌ Marking execution ${executionId} as failed: ${error instanceof Error ? error.message : String(error)}`);

    // Analyze recovery options
    const recoveryOptions = await this.recoveryManager.analyzeRecoveryOptions(executionId);
    const canRecover = this.recoveryManager.canRecover(executionId);

    // Create state snapshot for potential recovery
    if (canRecover) {
      try {
        await this.recoveryManager.createStateSnapshot(execution, {}, {
          failureReason: error instanceof Error ? error.message : String(error),
          failureTimestamp: new Date().toISOString()
        });
      } catch (snapshotError) {
        console.warn(`⚠️ Failed to create failure snapshot: ${snapshotError.message}`);
      }
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.FAILED,
      error: {
        message: error instanceof Error ? error.message : String(error),
        code: 'EXECUTION_FAILED',
        recoverable: canRecover,
        timestamp: new Date().toISOString(),
        stack: error instanceof Error ? error.stack : undefined
      },
      completedAt: new Date().toISOString(),
      metadata: {
        ...execution.metadata,
        failureAnalysis: {
          canRecover,
          recoveryOptionsCount: recoveryOptions.length,
          errorType: this.errorHandler ? this.inferErrorTypeFromMessage(error.message || '') : 'unknown',
          failureTimestamp: new Date().toISOString()
        }
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // Emit workflow failed event with recovery information
    await this.eventBus.emit(
      WorkflowEventType.WORKFLOW_FAILED,
      {
        executionId,
        workflowId: execution.workflowId,
        error: {
          message: error instanceof Error ? error.message : String(error),
          code: 'EXECUTION_FAILED',
          recoverable: canRecover
        },
        duration: new Date().getTime() - new Date(execution.startedAt).getTime(),
        stepCount: Object.keys(execution.stepResults).length,
        recoveryOptions: recoveryOptions.map(option => ({
          strategy: option.recoveryStrategy,
          riskLevel: option.riskLevel,
          estimatedTime: option.estimatedRecoveryTime
        }))
      },
      'workflow-engine',
      { executionId }
    );

    // Broadcast real-time workflow failed event
    this.realTimeBroadcaster.broadcastWorkflowFailed(executionId, execution.workflowId, error, {
      duration: new Date().getTime() - new Date(execution.startedAt).getTime(),
      stepCount: Object.keys(execution.stepResults).length,
      recoverable: canRecover,
      recoveryOptionsCount: recoveryOptions.length
    });

    console.log(`❌ Workflow execution ${executionId} failed. Recovery available: ${canRecover ? 'Yes' : 'No'}`);
    if (canRecover) {
      console.log(`🔧 ${recoveryOptions.length} recovery options available`);
    }
  }

  /**
   * Infer error type from error message (simplified version)
   */
  private inferErrorTypeFromMessage(message: string): string {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('timeout')) return 'timeout';
    if (lowerMessage.includes('network')) return 'network';
    if (lowerMessage.includes('ai') || lowerMessage.includes('generation')) return 'ai_generation';
    if (lowerMessage.includes('cms')) return 'cms_integration';
    if (lowerMessage.includes('validation')) return 'validation';
    return 'workflow_execution';
  }

  private async markStepFailed(executionId: string, stepId: string, error: any): Promise<void> {
    const stepResult: StepResult = {
      stepId,
      status: StepStatus.FAILED,
      inputs: {},
      outputs: {},
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    };

    await this.updateStepResult(executionId, stepId, stepResult);
  }

  /**
   * Get execution metrics including agent consultation data
   */
  async getExecutionMetrics(executionId: string): Promise<{
    execution: WorkflowExecution | null;
    consultationMetrics?: any;
    agentHealth?: any;
  }> {
    const execution = await this.getExecution(executionId);

    if (!execution) {
      return { execution: null };
    }

    // Get consultation metrics from the enhanced AI step
    const consultationMetrics = this.enhancedAIStep.getConsultationMetrics();
    const agentHealth = await this.enhancedAIStep.performHealthCheck();

    return {
      execution,
      consultationMetrics,
      agentHealth
    };
  }

  /**
   * Get agent consultation status for monitoring
   */
  async getAgentConsultationStatus(): Promise<{
    metrics: any;
    agentStatus: any[];
    healthCheck: any;
  }> {
    const metrics = this.enhancedAIStep.getConsultationMetrics();
    const agentStatus = await this.enhancedAIStep.getAgentStatus();
    const healthCheck = await this.enhancedAIStep.performHealthCheck();

    return {
      metrics,
      agentStatus,
      healthCheck
    };
  }

  /**
   * Execute Results Compilation step
   */
  private async executeResultsCompilation(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`📊 Executing results compilation step: ${step.id}`);

    try {
      // Initialize results compiler and metrics calculator
      const resultsCompiler = new ResultsCompiler();
      const metricsCalculator = new MetricsCalculator();

      // Compile comprehensive results package
      const resultsPackage: WorkflowResultsPackage = await resultsCompiler.compileResults(
        execution.id,
        execution.workflowId,
        execution.stepResults,
        execution.metadata
      );

      console.log(`📊 Results compilation completed for execution ${execution.id}`);
      console.log(`📊 Overall quality score: ${resultsPackage.qualityMetrics.overallQualityScore}`);
      console.log(`📊 SEO score: ${resultsPackage.seoMetrics.overallSEOScore}`);

      // Create results artifact
      const artifactId = uuidv4();
      const resultsArtifact: WorkflowArtifact = {
        id: artifactId,
        type: ArtifactType.FINAL_CONTENT,
        status: ArtifactStatus.COMPLETED,
        content: {
          resultsPackage,
          executionSummary: resultsPackage.executionSummary,
          contentSummary: resultsPackage.contentSummary,
          performanceMetrics: resultsPackage.performanceMetrics,
          qualityMetrics: resultsPackage.qualityMetrics,
          seoMetrics: resultsPackage.seoMetrics,
          recommendations: resultsPackage.recommendations
        },
        metadata: {
          stepId: step.id,
          executionId: execution.id,
          overallQualityScore: resultsPackage.qualityMetrics.overallQualityScore,
          seoScore: resultsPackage.seoMetrics.overallSEOScore,
          contentWordCount: resultsPackage.contentSummary.wordCount,
          executionDuration: resultsPackage.executionSummary.duration,
          stepsCompleted: resultsPackage.executionSummary.completedSteps
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Store the artifact
      await this.stateStore.setArtifact(artifactId, resultsArtifact);

      // Extract final content for publishing
      const finalContent = this.extractFinalContent(execution.stepResults);
      const publishingMetadata = this.generatePublishingMetadata(resultsPackage);

      console.log(`📊 Results compilation completed for step ${step.id}`);

      return {
        results_package: resultsPackage,
        final_content: finalContent,
        publishing_metadata: publishingMetadata,
        execution_summary: resultsPackage.executionSummary,
        content_summary: resultsPackage.contentSummary,
        performance_metrics: resultsPackage.performanceMetrics,
        quality_metrics: resultsPackage.qualityMetrics,
        seo_metrics: resultsPackage.seoMetrics,
        recommendations: resultsPackage.recommendations,
        export_metadata: resultsPackage.exportMetadata,
        artifact_id: artifactId,
        overall_quality_score: resultsPackage.qualityMetrics.overallQualityScore,
        seo_score: resultsPackage.seoMetrics.overallSEOScore,
        content_word_count: resultsPackage.contentSummary.wordCount
      };

    } catch (error) {
      console.error(`❌ Results compilation failed:`, error);
      throw new Error(`Results compilation failed: ${error.message}`);
    }
  }

  /**
   * Extract final content from step results
   */
  private extractFinalContent(stepResults: Record<string, StepResult>): string {
    // Look for content in priority order
    const contentSources = [
      'approved_content',
      'optimized_content',
      'final_content',
      'blog_content',
      'content'
    ];

    for (const [stepId, result] of Object.entries(stepResults)) {
      for (const source of contentSources) {
        if (result.outputs?.[source]) {
          console.log(`📝 Found final content from step ${stepId}, source: ${source}`);
          return result.outputs[source];
        }
      }
    }

    // Fallback: concatenate all content found
    let allContent = '';
    for (const result of Object.values(stepResults)) {
      if (result.outputs) {
        for (const [key, value] of Object.entries(result.outputs)) {
          if (typeof value === 'string' && value.length > 100 && key.includes('content')) {
            allContent += value + '\n\n';
          }
        }
      }
    }

    return allContent.trim() || 'No content found';
  }

  /**
   * Generate publishing metadata from results package
   */
  private generatePublishingMetadata(resultsPackage: WorkflowResultsPackage): Record<string, any> {
    return {
      title: resultsPackage.contentSummary.title,
      meta_description: resultsPackage.publishingMetadata.seoMetadata.description,
      keywords: resultsPackage.publishingMetadata.seoMetadata.keywords,
      categories: resultsPackage.publishingMetadata.categories,
      tags: resultsPackage.publishingMetadata.tags,
      schema_markup: resultsPackage.publishingMetadata.seoMetadata.schemaMarkup,
      publish_status: resultsPackage.publishingMetadata.publishStatus,
      content_type: resultsPackage.publishingMetadata.contentType,
      quality_score: resultsPackage.qualityMetrics.overallQualityScore,
      seo_score: resultsPackage.seoMetrics.overallSEOScore,
      word_count: resultsPackage.contentSummary.wordCount,
      readability_score: resultsPackage.contentSummary.readabilityScore
    };
  }

  /**
   * Execute SEO Optimization step
   */
  private async executeSEOOptimization(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`🔍 Executing SEO optimization step: ${step.id}`);

    try {
      // Extract content and metadata from inputs
      const content = inputs.approved_content || inputs.blog_content || inputs.content || '';
      const title = inputs.title || inputs.blog_title || 'Untitled';
      const keywordResearch = inputs.keyword_research || {};
      const reviewDecision = inputs.review_decision || {};

      // Extract keywords from keyword research
      let targetKeywords: string[] = [];
      if (typeof keywordResearch === 'string') {
        // Try to parse keywords from text
        const keywordMatches = keywordResearch.match(/(?:primary|main|target)\s*keyword[s]?[:\-\s]*([^\n\r.]+)/i);
        if (keywordMatches) {
          targetKeywords = [keywordMatches[1].trim()];
        }
      } else if (keywordResearch.primary_keyword) {
        targetKeywords = [keywordResearch.primary_keyword];
        if (keywordResearch.secondary_keywords) {
          targetKeywords.push(...keywordResearch.secondary_keywords);
        }
      }

      // Get SEO configuration from step config
      const seoConfig = step.config.seoConfig || {};
      if (seoConfig.targetKeywords) {
        targetKeywords = [...new Set([...targetKeywords, ...seoConfig.targetKeywords])];
      }

      console.log(`🔍 Analyzing content for SEO optimization...`);
      console.log(`📝 Content length: ${content.length} characters`);
      console.log(`🎯 Target keywords: ${targetKeywords.join(', ')}`);

      // Initialize SEO analyzer
      const seoAnalyzer = new SEOAnalyzer(seoConfig);

      // Perform SEO analysis
      const seoAnalysis: SEOAnalysisResult = await seoAnalyzer.analyzeContent(
        content,
        title,
        targetKeywords
      );

      console.log(`✅ SEO analysis complete. Score: ${seoAnalysis.score}/100`);

      // Create SEO artifact
      const artifactId = uuidv4();
      const seoArtifact: WorkflowArtifact = {
        id: artifactId,
        type: ArtifactType.SEO_OPTIMIZATION,
        status: ArtifactStatus.COMPLETED,
        content: {
          analysis: seoAnalysis,
          optimizedContent: content, // Content is already optimized from previous steps
          metaDescription: seoAnalysis.metaDescription,
          titleTag: seoAnalysis.titleTag,
          schemaMarkup: seoAnalysis.schemaMarkup,
          recommendations: seoAnalysis.recommendations
        },
        metadata: {
          stepId: step.id,
          executionId: execution.id,
          seoScore: seoAnalysis.score,
          keywordCount: targetKeywords.length,
          contentWordCount: seoAnalysis.contentMetrics.wordCount,
          readabilityScore: seoAnalysis.readabilityScore
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Store the artifact
      await this.stateStore.setArtifact(artifactId, seoArtifact);

      console.log(`📊 SEO optimization completed for step ${step.id}`);

      return {
        seo_analysis: seoAnalysis,
        seo_score: seoAnalysis.score,
        meta_description: seoAnalysis.metaDescription,
        title_tag: seoAnalysis.titleTag,
        schema_markup: seoAnalysis.schemaMarkup,
        recommendations: seoAnalysis.recommendations,
        keyword_analysis: seoAnalysis.keywordAnalysis,
        content_metrics: seoAnalysis.contentMetrics,
        readability_score: seoAnalysis.readabilityScore,
        internal_linking_suggestions: seoAnalysis.internalLinkingSuggestions,
        image_optimization_suggestions: seoAnalysis.imageOptimizationSuggestions,
        artifact_id: artifactId,
        optimized_content: content // Pass through the content for next steps
      };

    } catch (error) {
      console.error(`❌ SEO optimization failed:`, error);
      throw new Error(`SEO optimization failed: ${error.message}`);
    }
  }

  /**
   * Execute CMS Integration step
   */
  private async executeCMSIntegration(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`📝 Executing CMS integration step: ${step.id}`);

    const config = step.config.cmsConfig;
    if (!config) {
      throw new Error('CMS configuration is required for CMS integration step');
    }

    try {
      // Extract content and metadata from inputs
      const finalContent = inputs.final_content || inputs.content || inputs.blog_content;
      const publishingMetadata = inputs.publishing_metadata || {};
      const resultsPackage = inputs.results_package || {};

      if (!finalContent) {
        throw new Error('No content found for CMS publishing');
      }

      // For Payload CMS integration
      if (config.platform === 'payload') {
        console.log(`📝 Publishing to Payload CMS...`);

        // Initialize Payload integration
        const payloadIntegration = new PayloadIntegration();

        // Prepare publish request
        const publishRequest: PayloadPublishRequest = {
          title: publishingMetadata.title || resultsPackage.contentSummary?.title || 'Generated Blog Post',
          content: finalContent,
          status: (config.publishStatus as 'draft' | 'published') || 'draft',
          meta: {
            description: publishingMetadata.meta_description || resultsPackage.seoMetrics?.metaOptimization || '',
            keywords: publishingMetadata.keywords || resultsPackage.publishingMetadata?.seoMetadata?.keywords?.join(', ') || '',
          },
          categories: publishingMetadata.categories || resultsPackage.publishingMetadata?.categories || [],
          tags: publishingMetadata.tags || resultsPackage.publishingMetadata?.tags || [],
          author: execution.metadata.userId || 'workflow-system',
          publishedDate: config.autoPublish ? new Date().toISOString() : undefined,
          workflow: {
            executionId: execution.id,
            templateId: execution.metadata.templateId || '',
            generatedAt: new Date().toISOString(),
            agentConsultations: resultsPackage.metadata?.agent_consultations
          },
          seoMetadata: {
            title: publishingMetadata.title || resultsPackage.contentSummary?.title || 'Generated Blog Post',
            description: publishingMetadata.meta_description || resultsPackage.seoMetrics?.metaOptimization || '',
            keywords: publishingMetadata.keywords || resultsPackage.publishingMetadata?.seoMetadata?.keywords || [],
            schemaMarkup: publishingMetadata.schema_markup || resultsPackage.publishingMetadata?.seoMetadata?.schemaMarkup
          }
        };

        console.log(`📝 Publishing content with title: "${publishRequest.title}"`);

        // Publish to Payload CMS
        const publishResult = await payloadIntegration.publishContent(publishRequest);

        console.log(`✅ Content published to Payload CMS with ID: ${publishResult.id}`);

        return {
          cms_post_id: publishResult.id,
          cms_url: publishResult.url,
          publish_status: publishResult.status,
          published_at: publishResult.publishedAt || new Date().toISOString(),
          cms_platform: 'payload',
          collection: publishResult.collection,
          slug: publishResult.slug,
          post_data: publishRequest
        };
      }

      // For other CMS platforms (WordPress, Shopify, etc.)
      else {
        console.log(`📝 Publishing to ${config.platform}...`);

        // Placeholder for other CMS integrations
        const cmsPostId = `${config.platform}-${Date.now()}`;
        const cmsUrl = `${config.endpoint || ''}/posts/${cmsPostId}`;

        return {
          cms_post_id: cmsPostId,
          cms_url: cmsUrl,
          publish_status: config.publishStatus || 'draft',
          published_at: new Date().toISOString(),
          cms_platform: config.platform
        };
      }

    } catch (error) {
      console.error(`❌ CMS integration failed:`, error);
      throw new Error(`CMS integration failed: ${error.message}`);
    }
  }

  /**
   * Execute Workflow Completion step
   */
  private async executeCompletion(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`🎉 Executing workflow completion step: ${step.id}`);

    const config: CompletionConfig = step.config.completionConfig || {};

    try {
      // Initialize completion handler
      const completionHandler = new WorkflowCompletionHandler();

      // Handle workflow completion with comprehensive processing
      const completionReport: CompletionReport = await completionHandler.handleCompletion(
        execution,
        config,
        inputs
      );

      // Generate results URL
      const resultsUrl = `/workflow/result/${execution.id}`;

      // Update execution status to completed
      const updatedExecution: WorkflowExecution = {
        ...execution,
        status: ExecutionStatus.COMPLETED,
        completedAt: completionReport.completedAt,
        progress: 100,
        outputs: {
          ...execution.outputs,
          completion_report: completionReport,
          results_url: resultsUrl,
          workflow_summary: this.generateWorkflowSummary(completionReport)
        }
      };

      await this.stateStore.setExecution(updatedExecution);

      // Send notifications if configured
      if (config.notifyUser || config.enableWebhooks || config.emailNotifications) {
        await this.sendCompletionNotifications(completionReport, config);
      }

      console.log(`🎉 Workflow ${execution.id} completed successfully!`);
      console.log(`📊 Completion Summary: ${completionReport.summary.stepsCompleted}/${completionReport.summary.totalSteps} steps completed`);
      console.log(`📊 Quality Score: ${completionReport.summary.qualityScore || 'N/A'}`);
      console.log(`📊 SEO Score: ${completionReport.summary.seoScore || 'N/A'}`);

      return {
        completion_report: completionReport,
        results_url: resultsUrl,
        workflow_summary: this.generateWorkflowSummary(completionReport),
        redirect_url: config.redirectToResults ? resultsUrl : undefined,
        completed_at: completionReport.completedAt,
        execution_status: completionReport.status,
        notifications: completionReport.notifications,
        archive_info: completionReport.archiveInfo,
        final_content: completionReport.outputs.finalContent,
        cms_details: completionReport.outputs.cmsDetails,
        export_urls: completionReport.outputs.exportUrls,
        download_links: completionReport.outputs.downloadLinks
      };

    } catch (error) {
      console.error(`❌ Workflow completion failed:`, error);

      // Update execution status to failed
      const failedExecution: WorkflowExecution = {
        ...execution,
        status: ExecutionStatus.FAILED,
        completedAt: new Date().toISOString(),
        error: {
          message: error.message,
          code: 'COMPLETION_FAILED',
          timestamp: new Date().toISOString()
        }
      };

      await this.stateStore.setExecution(failedExecution);

      throw new Error(`Workflow completion failed: ${error.message}`);
    }
  }

  /**
   * Generate workflow summary message
   */
  private generateWorkflowSummary(completionReport: CompletionReport): string {
    const { summary, outputs } = completionReport;

    let summaryMessage = `Workflow completed successfully. `;
    summaryMessage += `${summary.stepsCompleted}/${summary.totalSteps} steps completed `;
    summaryMessage += `(${summary.successRate.toFixed(1)}% success rate). `;

    if (outputs.cmsDetails?.postId) {
      summaryMessage += `Content published to CMS with ID: ${outputs.cmsDetails.postId}. `;
    } else if (outputs.finalContent) {
      summaryMessage += `Content ready for publishing. `;
    }

    if (summary.qualityScore) {
      summaryMessage += `Quality score: ${summary.qualityScore}/100. `;
    }

    if (summary.seoScore) {
      summaryMessage += `SEO score: ${summary.seoScore}/100.`;
    }

    return summaryMessage.trim();
  }

  /**
   * Send completion notifications
   */
  private async sendCompletionNotifications(
    completionReport: CompletionReport,
    config: CompletionConfig
  ): Promise<void> {
    try {
      const notifier = new CompletionNotifier({
        email: {
          enabled: config.emailNotifications || false,
          recipients: config.emailRecipients || []
        },
        webhook: {
          enabled: config.enableWebhooks || false,
          urls: config.webhookUrls || []
        },
        internal: {
          enabled: true,
          logLevel: 'info'
        }
      });

      const notificationPayload: NotificationPayload = {
        executionId: completionReport.executionId,
        workflowId: completionReport.workflowId,
        status: completionReport.status,
        timestamp: completionReport.completedAt,
        summary: {
          stepsCompleted: completionReport.summary.stepsCompleted,
          totalSteps: completionReport.summary.totalSteps,
          duration: completionReport.duration,
          successRate: completionReport.summary.successRate
        },
        outputs: {
          finalContent: completionReport.summary.finalContent,
          cmsPublished: completionReport.summary.cmsPublished,
          cmsPostId: completionReport.summary.cmsPostId,
          qualityScore: completionReport.summary.qualityScore
        },
        links: {
          resultsUrl: `/workflow/result/${completionReport.executionId}`,
          cmsUrl: completionReport.outputs.cmsDetails?.url,
          downloadUrls: completionReport.outputs.downloadLinks
        }
      };

      await notifier.sendNotifications(notificationPayload);
      console.log(`📢 Completion notifications sent for execution ${completionReport.executionId}`);

    } catch (error) {
      console.error(`❌ Failed to send completion notifications:`, error);
      // Don't throw error here as it shouldn't fail the workflow completion
    }
  }
}


