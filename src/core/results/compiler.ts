/**
 * Results Compiler
 * Aggregates and compiles workflow results into comprehensive packages
 */

export interface WorkflowResultsPackage {
  executionId: string;
  workflowId: string;
  executionSummary: ExecutionSummary;
  contentSummary: ContentSummary;
  performanceMetrics: PerformanceMetrics;
  qualityMetrics: QualityMetrics;
  seoMetrics: SEOMetrics;
  stepResults: StepResultSummary[];
  recommendations: Recommendation[];
  exportMetadata: ExportMetadata;
  publishingMetadata: PublishingMetadata;
}

export interface ExecutionSummary {
  startTime: string;
  endTime: string;
  duration: number;
  status: 'completed' | 'failed' | 'partial';
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  skippedSteps: number;
  humanInterventions: number;
  regenerationCount: number;
}

export interface ContentSummary {
  title: string;
  wordCount: number;
  characterCount: number;
  paragraphCount: number;
  headingCount: number;
  imageCount: number;
  linkCount: number;
  keywordCount: number;
  readabilityScore: number;
  contentType: string;
  language: string;
}

export interface PerformanceMetrics {
  totalExecutionTime: number;
  averageStepTime: number;
  aiGenerationTime: number;
  humanReviewTime: number;
  processingEfficiency: number;
  resourceUtilization: ResourceUtilization;
  bottlenecks: Bottleneck[];
}

export interface ResourceUtilization {
  aiTokensUsed: number;
  aiCost: number;
  memoryUsage: number;
  processingTime: number;
}

export interface Bottleneck {
  stepId: string;
  stepName: string;
  duration: number;
  reason: string;
  impact: 'high' | 'medium' | 'low';
}

export interface QualityMetrics {
  overallQualityScore: number;
  contentQualityScore: number;
  seoQualityScore: number;
  technicalQualityScore: number;
  userSatisfactionScore: number;
  qualityFactors: QualityFactor[];
}

export interface QualityFactor {
  name: string;
  score: number;
  weight: number;
  description: string;
}

export interface SEOMetrics {
  overallSEOScore: number;
  keywordOptimization: number;
  contentStructure: number;
  technicalSEO: number;
  readability: number;
  metaOptimization: number;
  recommendations: string[];
}

export interface StepResultSummary {
  stepId: string;
  stepName: string;
  stepType: string;
  status: string;
  duration: number;
  inputCount: number;
  outputCount: number;
  artifactId?: string;
  qualityScore?: number;
  issues: string[];
  achievements: string[];
}

export interface Recommendation {
  type: 'content' | 'seo' | 'performance' | 'quality';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  actionItems: string[];
  expectedImpact: string;
  effort: 'low' | 'medium' | 'high';
}

export interface ExportMetadata {
  formats: string[];
  downloadUrls: Record<string, string>;
  fileSize: number;
  generatedAt: string;
  expiresAt: string;
}

export interface PublishingMetadata {
  platform: string;
  contentType: string;
  publishStatus: 'draft' | 'published' | 'scheduled';
  publishUrl?: string;
  categories: string[];
  tags: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
    schemaMarkup?: Record<string, any>;
  };
}

export class ResultsCompiler {
  /**
   * Compile comprehensive results package from workflow execution
   */
  async compileResults(
    executionId: string,
    workflowId: string,
    stepResults: Record<string, any>,
    executionMetadata: any
  ): Promise<WorkflowResultsPackage> {
    console.log(`📊 Compiling results for execution ${executionId}`);

    const executionSummary = this.generateExecutionSummary(stepResults, executionMetadata);
    const contentSummary = this.generateContentSummary(stepResults);
    const performanceMetrics = this.calculatePerformanceMetrics(stepResults, executionMetadata);
    const qualityMetrics = this.calculateQualityMetrics(stepResults);
    const seoMetrics = this.extractSEOMetrics(stepResults);
    const stepResultsSummary = this.summarizeStepResults(stepResults);
    const recommendations = this.generateRecommendations(stepResults, qualityMetrics, seoMetrics);
    const exportMetadata = this.generateExportMetadata(executionId);
    const publishingMetadata = this.generatePublishingMetadata(stepResults);

    const resultsPackage: WorkflowResultsPackage = {
      executionId,
      workflowId,
      executionSummary,
      contentSummary,
      performanceMetrics,
      qualityMetrics,
      seoMetrics,
      stepResults: stepResultsSummary,
      recommendations,
      exportMetadata,
      publishingMetadata
    };

    console.log(`✅ Results compilation complete for execution ${executionId}`);
    return resultsPackage;
  }

  /**
   * Generate execution summary
   */
  private generateExecutionSummary(stepResults: Record<string, any>, executionMetadata: any): ExecutionSummary {
    const steps = Object.values(stepResults);
    const startTime = executionMetadata.startedAt || new Date().toISOString();
    const endTime = new Date().toISOString();
    const duration = new Date(endTime).getTime() - new Date(startTime).getTime();

    const completedSteps = steps.filter((step: any) => step.status === 'completed').length;
    const failedSteps = steps.filter((step: any) => step.status === 'failed').length;
    const skippedSteps = steps.filter((step: any) => step.status === 'skipped').length;
    const humanInterventions = steps.filter((step: any) => 
      step.stepType === 'human_review' || step.stepType === 'approval_gate'
    ).length;

    return {
      startTime,
      endTime,
      duration,
      status: failedSteps > 0 ? 'failed' : completedSteps === steps.length ? 'completed' : 'partial',
      totalSteps: steps.length,
      completedSteps,
      failedSteps,
      skippedSteps,
      humanInterventions,
      regenerationCount: 0 // TODO: Track regenerations
    };
  }

  /**
   * Generate content summary
   */
  private generateContentSummary(stepResults: Record<string, any>): ContentSummary {
    // Find the final content from results compilation or approved content
    let finalContent = '';
    let title = 'Untitled';

    // Look for content in various steps
    for (const [stepId, result] of Object.entries(stepResults)) {
      const stepResult = result as any;
      if (stepResult.outputs?.approved_content) {
        finalContent = stepResult.outputs.approved_content;
      } else if (stepResult.outputs?.final_content) {
        finalContent = stepResult.outputs.final_content;
      } else if (stepResult.outputs?.blog_content) {
        finalContent = stepResult.outputs.blog_content;
      }

      if (stepResult.outputs?.title || stepResult.outputs?.blog_title) {
        title = stepResult.outputs.title || stepResult.outputs.blog_title;
      }
    }

    // Analyze content
    const wordCount = (finalContent.match(/\b\w+\b/g) || []).length;
    const characterCount = finalContent.length;
    const paragraphCount = finalContent.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    const headingCount = (finalContent.match(/^#{1,6}\s+/gm) || []).length;
    const imageCount = (finalContent.match(/!\[.*?\]\(.*?\)/g) || []).length;
    const linkCount = (finalContent.match(/\[.*?\]\(.*?\)/g) || []).length;

    // Extract readability score from SEO analysis if available
    let readabilityScore = 0;
    for (const result of Object.values(stepResults)) {
      const stepResult = result as any;
      if (stepResult.outputs?.seo_analysis?.readabilityScore) {
        readabilityScore = stepResult.outputs.seo_analysis.readabilityScore;
        break;
      }
    }

    return {
      title,
      wordCount,
      characterCount,
      paragraphCount,
      headingCount,
      imageCount,
      linkCount,
      keywordCount: 0, // TODO: Extract from keyword research
      readabilityScore,
      contentType: 'blog_post',
      language: 'en'
    };
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(stepResults: Record<string, any>, executionMetadata: any): PerformanceMetrics {
    const steps = Object.values(stepResults) as any[];
    const totalExecutionTime = steps.reduce((total, step) => total + (step.duration || 0), 0);
    const averageStepTime = totalExecutionTime / steps.length;

    const aiSteps = steps.filter(step => step.stepType === 'ai_generation');
    const aiGenerationTime = aiSteps.reduce((total, step) => total + (step.duration || 0), 0);

    const humanSteps = steps.filter(step => 
      step.stepType === 'human_review' || step.stepType === 'approval_gate'
    );
    const humanReviewTime = humanSteps.reduce((total, step) => total + (step.duration || 0), 0);

    const processingEfficiency = totalExecutionTime > 0 ? 
      (aiGenerationTime / totalExecutionTime) * 100 : 0;

    // Identify bottlenecks
    const bottlenecks: Bottleneck[] = steps
      .filter(step => step.duration > averageStepTime * 2)
      .map(step => ({
        stepId: step.stepId,
        stepName: step.stepName || step.stepId,
        duration: step.duration,
        reason: step.stepType === 'human_review' ? 'Human review required' : 'Processing complexity',
        impact: step.duration > averageStepTime * 5 ? 'high' : 'medium'
      }));

    return {
      totalExecutionTime,
      averageStepTime,
      aiGenerationTime,
      humanReviewTime,
      processingEfficiency,
      resourceUtilization: {
        aiTokensUsed: 0, // TODO: Track from AI calls
        aiCost: 0,
        memoryUsage: 0,
        processingTime: totalExecutionTime
      },
      bottlenecks
    };
  }

  /**
   * Calculate quality metrics
   */
  private calculateQualityMetrics(stepResults: Record<string, any>): QualityMetrics {
    const qualityFactors: QualityFactor[] = [];
    let totalScore = 0;
    let totalWeight = 0;

    // Content quality from SEO analysis
    let contentQualityScore = 70; // Default
    let seoQualityScore = 70; // Default

    for (const result of Object.values(stepResults)) {
      const stepResult = result as any;
      if (stepResult.outputs?.seo_analysis) {
        seoQualityScore = stepResult.outputs.seo_analysis.score || 70;
        contentQualityScore = stepResult.outputs.seo_analysis.contentMetrics ? 80 : 70;
        break;
      }
    }

    qualityFactors.push(
      { name: 'Content Quality', score: contentQualityScore, weight: 0.4, description: 'Overall content quality and structure' },
      { name: 'SEO Quality', score: seoQualityScore, weight: 0.3, description: 'Search engine optimization quality' },
      { name: 'Technical Quality', score: 85, weight: 0.2, description: 'Technical implementation quality' },
      { name: 'User Experience', score: 80, weight: 0.1, description: 'User experience and readability' }
    );

    // Calculate weighted average
    for (const factor of qualityFactors) {
      totalScore += factor.score * factor.weight;
      totalWeight += factor.weight;
    }

    const overallQualityScore = totalWeight > 0 ? totalScore / totalWeight : 70;

    return {
      overallQualityScore,
      contentQualityScore,
      seoQualityScore,
      technicalQualityScore: 85,
      userSatisfactionScore: 80,
      qualityFactors
    };
  }

  /**
   * Extract SEO metrics
   */
  private extractSEOMetrics(stepResults: Record<string, any>): SEOMetrics {
    // Find SEO analysis results
    for (const result of Object.values(stepResults)) {
      const stepResult = result as any;
      if (stepResult.outputs?.seo_analysis) {
        const seoAnalysis = stepResult.outputs.seo_analysis;
        return {
          overallSEOScore: seoAnalysis.score || 70,
          keywordOptimization: seoAnalysis.keywordAnalysis?.keywordDensity > 0 ? 80 : 60,
          contentStructure: seoAnalysis.contentMetrics?.headingStructure?.h1Count === 1 ? 90 : 70,
          technicalSEO: seoAnalysis.schemaMarkup ? 85 : 60,
          readability: seoAnalysis.readabilityScore || 70,
          metaOptimization: seoAnalysis.metaDescription ? 85 : 60,
          recommendations: seoAnalysis.recommendations?.map((r: any) => r.title) || []
        };
      }
    }

    // Default SEO metrics if no analysis found
    return {
      overallSEOScore: 70,
      keywordOptimization: 70,
      contentStructure: 70,
      technicalSEO: 70,
      readability: 70,
      metaOptimization: 70,
      recommendations: []
    };
  }

  /**
   * Summarize step results
   */
  private summarizeStepResults(stepResults: Record<string, any>): StepResultSummary[] {
    return Object.entries(stepResults).map(([stepId, result]) => {
      const stepResult = result as any;
      return {
        stepId,
        stepName: stepResult.stepName || stepId,
        stepType: stepResult.stepType || 'unknown',
        status: stepResult.status || 'unknown',
        duration: stepResult.duration || 0,
        inputCount: Object.keys(stepResult.inputs || {}).length,
        outputCount: Object.keys(stepResult.outputs || {}).length,
        artifactId: stepResult.artifactId,
        qualityScore: stepResult.outputs?.seo_score || undefined,
        issues: [], // TODO: Extract issues from step results
        achievements: [] // TODO: Extract achievements
      };
    });
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    stepResults: Record<string, any>,
    qualityMetrics: QualityMetrics,
    seoMetrics: SEOMetrics
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Quality-based recommendations
    if (qualityMetrics.overallQualityScore < 80) {
      recommendations.push({
        type: 'quality',
        priority: 'high',
        title: 'Improve Overall Quality',
        description: 'The content quality score is below optimal. Consider reviewing and enhancing the content.',
        actionItems: ['Review content structure', 'Enhance readability', 'Add more detailed information'],
        expectedImpact: 'Improved user engagement and search rankings',
        effort: 'medium'
      });
    }

    // SEO-based recommendations
    if (seoMetrics.overallSEOScore < 80) {
      recommendations.push({
        type: 'seo',
        priority: 'high',
        title: 'Enhance SEO Optimization',
        description: 'SEO score can be improved for better search engine visibility.',
        actionItems: ['Optimize keyword density', 'Improve meta descriptions', 'Add schema markup'],
        expectedImpact: 'Better search engine rankings and organic traffic',
        effort: 'low'
      });
    }

    return recommendations;
  }

  /**
   * Generate export metadata
   */
  private generateExportMetadata(executionId: string): ExportMetadata {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days

    return {
      formats: ['json', 'pdf', 'html', 'docx'],
      downloadUrls: {
        json: `/api/workflow/export/${executionId}/json`,
        pdf: `/api/workflow/export/${executionId}/pdf`,
        html: `/api/workflow/export/${executionId}/html`,
        docx: `/api/workflow/export/${executionId}/docx`
      },
      fileSize: 0, // TODO: Calculate actual file size
      generatedAt: now.toISOString(),
      expiresAt: expiresAt.toISOString()
    };
  }

  /**
   * Generate publishing metadata
   */
  private generatePublishingMetadata(stepResults: Record<string, any>): PublishingMetadata {
    // Extract publishing information from CMS integration step
    for (const result of Object.values(stepResults)) {
      const stepResult = result as any;
      if (stepResult.outputs?.cms_post_id) {
        return {
          platform: stepResult.outputs.cms_platform || 'payload',
          contentType: 'blog_post',
          publishStatus: stepResult.outputs.publish_status || 'draft',
          publishUrl: stepResult.outputs.cms_url,
          categories: [],
          tags: [],
          seoMetadata: {
            title: stepResult.outputs.title_tag || 'Untitled',
            description: stepResult.outputs.meta_description || '',
            keywords: [],
            schemaMarkup: stepResult.outputs.schema_markup
          }
        };
      }
    }

    // Default publishing metadata
    return {
      platform: 'payload',
      contentType: 'blog_post',
      publishStatus: 'draft',
      categories: [],
      tags: [],
      seoMetadata: {
        title: 'Untitled',
        description: '',
        keywords: []
      }
    };
  }
}
