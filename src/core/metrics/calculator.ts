/**
 * Metrics Calculator
 * Calculates performance and quality metrics for workflow executions
 */

export interface MetricsData {
  executionMetrics: ExecutionMetrics;
  performanceMetrics: PerformanceMetrics;
  qualityMetrics: QualityMetrics;
  efficiencyMetrics: EfficiencyMetrics;
  costMetrics: CostMetrics;
}

export interface ExecutionMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  failureRate: number;
  averageStepsPerExecution: number;
}

export interface PerformanceMetrics {
  averageStepDuration: number;
  fastestStepDuration: number;
  slowestStepDuration: number;
  bottleneckSteps: string[];
  throughput: number;
  latency: number;
  resourceUtilization: number;
}

export interface QualityMetrics {
  averageQualityScore: number;
  contentQualityTrend: number[];
  seoQualityTrend: number[];
  userSatisfactionScore: number;
  regenerationRate: number;
  approvalRate: number;
}

export interface EfficiencyMetrics {
  automationRate: number;
  humanInterventionRate: number;
  processingEfficiency: number;
  resourceEfficiency: number;
  timeToValue: number;
}

export interface CostMetrics {
  totalCost: number;
  costPerExecution: number;
  aiCosts: number;
  humanCosts: number;
  infrastructureCosts: number;
  costTrend: number[];
}

export interface StepMetrics {
  stepId: string;
  stepType: string;
  averageDuration: number;
  successRate: number;
  failureRate: number;
  qualityScore: number;
  cost: number;
  frequency: number;
}

export interface TrendData {
  timestamp: string;
  value: number;
  label?: string;
}

export class MetricsCalculator {
  /**
   * Calculate comprehensive metrics from execution data
   */
  calculateMetrics(executions: any[], timeRange: string = '30d'): MetricsData {
    console.log(`📊 Calculating metrics for ${executions.length} executions over ${timeRange}`);

    const executionMetrics = this.calculateExecutionMetrics(executions);
    const performanceMetrics = this.calculatePerformanceMetrics(executions);
    const qualityMetrics = this.calculateQualityMetrics(executions);
    const efficiencyMetrics = this.calculateEfficiencyMetrics(executions);
    const costMetrics = this.calculateCostMetrics(executions);

    return {
      executionMetrics,
      performanceMetrics,
      qualityMetrics,
      efficiencyMetrics,
      costMetrics
    };
  }

  /**
   * Calculate execution-level metrics
   */
  private calculateExecutionMetrics(executions: any[]): ExecutionMetrics {
    const totalExecutions = executions.length;
    const successfulExecutions = executions.filter(e => e.status === 'completed').length;
    const failedExecutions = executions.filter(e => e.status === 'failed').length;

    const executionTimes = executions
      .filter(e => e.startedAt && e.completedAt)
      .map(e => new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime());

    const averageExecutionTime = executionTimes.length > 0 
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
      : 0;

    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
    const failureRate = totalExecutions > 0 ? (failedExecutions / totalExecutions) * 100 : 0;

    const totalSteps = executions.reduce((sum, e) => sum + Object.keys(e.stepResults || {}).length, 0);
    const averageStepsPerExecution = totalExecutions > 0 ? totalSteps / totalExecutions : 0;

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageExecutionTime,
      successRate,
      failureRate,
      averageStepsPerExecution
    };
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(executions: any[]): PerformanceMetrics {
    const allSteps = executions.flatMap(e => Object.values(e.stepResults || {}));
    const stepDurations = allSteps
      .filter((step: any) => step.duration)
      .map((step: any) => step.duration);

    const averageStepDuration = stepDurations.length > 0
      ? stepDurations.reduce((sum, duration) => sum + duration, 0) / stepDurations.length
      : 0;

    const fastestStepDuration = stepDurations.length > 0 ? Math.min(...stepDurations) : 0;
    const slowestStepDuration = stepDurations.length > 0 ? Math.max(...stepDurations) : 0;

    // Identify bottleneck steps (steps that take significantly longer than average)
    const bottleneckThreshold = averageStepDuration * 2;
    const bottleneckSteps = allSteps
      .filter((step: any) => step.duration > bottleneckThreshold)
      .map((step: any) => step.stepId)
      .filter((stepId, index, array) => array.indexOf(stepId) === index); // Remove duplicates

    // Calculate throughput (executions per hour)
    const completedExecutions = executions.filter(e => e.status === 'completed');
    const timeSpan = this.calculateTimeSpan(executions);
    const throughput = timeSpan > 0 ? (completedExecutions.length / timeSpan) * 3600000 : 0; // per hour

    // Calculate average latency
    const latency = averageStepDuration;

    // Resource utilization (simplified)
    const resourceUtilization = Math.min(100, (averageStepDuration / 60000) * 10); // Simplified calculation

    return {
      averageStepDuration,
      fastestStepDuration,
      slowestStepDuration,
      bottleneckSteps,
      throughput,
      latency,
      resourceUtilization
    };
  }

  /**
   * Calculate quality metrics
   */
  private calculateQualityMetrics(executions: any[]): QualityMetrics {
    const qualityScores: number[] = [];
    const contentQualityTrend: number[] = [];
    const seoQualityTrend: number[] = [];
    let totalRegenerations = 0;
    let totalApprovals = 0;
    let totalReviews = 0;

    executions.forEach(execution => {
      const stepResults = Object.values(execution.stepResults || {});
      
      // Extract quality scores
      stepResults.forEach((step: any) => {
        if (step.outputs?.seo_analysis?.score) {
          qualityScores.push(step.outputs.seo_analysis.score);
          seoQualityTrend.push(step.outputs.seo_analysis.score);
        }
        
        if (step.outputs?.quality_score) {
          qualityScores.push(step.outputs.quality_score);
          contentQualityTrend.push(step.outputs.quality_score);
        }

        // Count regenerations and approvals
        if (step.stepType === 'human_review') {
          totalReviews++;
          if (step.outputs?.review_decision === 'approved') {
            totalApprovals++;
          } else if (step.outputs?.review_decision === 'regenerate') {
            totalRegenerations++;
          }
        }
      });
    });

    const averageQualityScore = qualityScores.length > 0
      ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
      : 70;

    const regenerationRate = totalReviews > 0 ? (totalRegenerations / totalReviews) * 100 : 0;
    const approvalRate = totalReviews > 0 ? (totalApprovals / totalReviews) * 100 : 0;

    // User satisfaction score (derived from approval rate and quality scores)
    const userSatisfactionScore = (approvalRate + averageQualityScore) / 2;

    return {
      averageQualityScore,
      contentQualityTrend,
      seoQualityTrend,
      userSatisfactionScore,
      regenerationRate,
      approvalRate
    };
  }

  /**
   * Calculate efficiency metrics
   */
  private calculateEfficiencyMetrics(executions: any[]): EfficiencyMetrics {
    let totalSteps = 0;
    let automatedSteps = 0;
    let humanSteps = 0;
    let totalProcessingTime = 0;
    let totalWaitTime = 0;

    executions.forEach(execution => {
      const stepResults = Object.values(execution.stepResults || {});
      
      stepResults.forEach((step: any) => {
        totalSteps++;
        
        if (step.stepType === 'human_review' || step.stepType === 'approval_gate') {
          humanSteps++;
          totalWaitTime += step.duration || 0;
        } else {
          automatedSteps++;
          totalProcessingTime += step.duration || 0;
        }
      });
    });

    const automationRate = totalSteps > 0 ? (automatedSteps / totalSteps) * 100 : 0;
    const humanInterventionRate = totalSteps > 0 ? (humanSteps / totalSteps) * 100 : 0;

    const totalTime = totalProcessingTime + totalWaitTime;
    const processingEfficiency = totalTime > 0 ? (totalProcessingTime / totalTime) * 100 : 0;

    // Resource efficiency (processing time vs total time)
    const resourceEfficiency = totalTime > 0 ? (totalProcessingTime / totalTime) * 100 : 0;

    // Time to value (average time from start to first valuable output)
    const timeToValue = executions.length > 0
      ? executions.reduce((sum, e) => {
          const firstValueStep = Object.values(e.stepResults || {})
            .find((step: any) => step.stepType === 'ai_generation' && step.status === 'completed');
          return sum + (firstValueStep ? (firstValueStep as any).duration || 0 : 0);
        }, 0) / executions.length
      : 0;

    return {
      automationRate,
      humanInterventionRate,
      processingEfficiency,
      resourceEfficiency,
      timeToValue
    };
  }

  /**
   * Calculate cost metrics
   */
  private calculateCostMetrics(executions: any[]): CostMetrics {
    let totalAiCosts = 0;
    let totalHumanCosts = 0;
    let totalInfrastructureCosts = 0;
    const costTrend: number[] = [];

    executions.forEach(execution => {
      let executionCost = 0;
      const stepResults = Object.values(execution.stepResults || {});
      
      stepResults.forEach((step: any) => {
        // AI costs (simplified calculation)
        if (step.stepType === 'ai_generation') {
          const aiCost = this.calculateAICost(step);
          totalAiCosts += aiCost;
          executionCost += aiCost;
        }
        
        // Human costs (simplified calculation)
        if (step.stepType === 'human_review') {
          const humanCost = this.calculateHumanCost(step);
          totalHumanCosts += humanCost;
          executionCost += humanCost;
        }
      });

      // Infrastructure costs (simplified)
      const infraCost = this.calculateInfrastructureCost(execution);
      totalInfrastructureCosts += infraCost;
      executionCost += infraCost;

      costTrend.push(executionCost);
    });

    const totalCost = totalAiCosts + totalHumanCosts + totalInfrastructureCosts;
    const costPerExecution = executions.length > 0 ? totalCost / executions.length : 0;

    return {
      totalCost,
      costPerExecution,
      aiCosts: totalAiCosts,
      humanCosts: totalHumanCosts,
      infrastructureCosts: totalInfrastructureCosts,
      costTrend
    };
  }

  /**
   * Calculate step-level metrics
   */
  calculateStepMetrics(executions: any[]): StepMetrics[] {
    const stepMap = new Map<string, any>();

    executions.forEach(execution => {
      Object.values(execution.stepResults || {}).forEach((step: any) => {
        const key = `${step.stepType}-${step.stepId}`;
        
        if (!stepMap.has(key)) {
          stepMap.set(key, {
            stepId: step.stepId,
            stepType: step.stepType,
            durations: [],
            successes: 0,
            failures: 0,
            qualityScores: [],
            costs: [],
            frequency: 0
          });
        }

        const metrics = stepMap.get(key);
        metrics.frequency++;
        
        if (step.duration) metrics.durations.push(step.duration);
        if (step.status === 'completed') metrics.successes++;
        if (step.status === 'failed') metrics.failures++;
        if (step.outputs?.quality_score) metrics.qualityScores.push(step.outputs.quality_score);
        
        // Calculate step cost
        const stepCost = this.calculateStepCost(step);
        metrics.costs.push(stepCost);
      });
    });

    return Array.from(stepMap.values()).map(metrics => ({
      stepId: metrics.stepId,
      stepType: metrics.stepType,
      averageDuration: metrics.durations.length > 0
        ? metrics.durations.reduce((sum: number, d: number) => sum + d, 0) / metrics.durations.length
        : 0,
      successRate: metrics.frequency > 0 ? (metrics.successes / metrics.frequency) * 100 : 0,
      failureRate: metrics.frequency > 0 ? (metrics.failures / metrics.frequency) * 100 : 0,
      qualityScore: metrics.qualityScores.length > 0
        ? metrics.qualityScores.reduce((sum: number, s: number) => sum + s, 0) / metrics.qualityScores.length
        : 0,
      cost: metrics.costs.length > 0
        ? metrics.costs.reduce((sum: number, c: number) => sum + c, 0) / metrics.costs.length
        : 0,
      frequency: metrics.frequency
    }));
  }

  /**
   * Calculate time span of executions
   */
  private calculateTimeSpan(executions: any[]): number {
    if (executions.length === 0) return 0;

    const timestamps = executions
      .filter(e => e.startedAt)
      .map(e => new Date(e.startedAt).getTime());

    if (timestamps.length === 0) return 0;

    const earliest = Math.min(...timestamps);
    const latest = Math.max(...timestamps);
    
    return latest - earliest;
  }

  /**
   * Calculate AI cost for a step (simplified)
   */
  private calculateAICost(step: any): number {
    // Simplified cost calculation based on step duration and type
    const baseCost = 0.01; // Base cost per step
    const durationMultiplier = (step.duration || 1000) / 1000; // Cost increases with duration
    return baseCost * durationMultiplier;
  }

  /**
   * Calculate human cost for a step (simplified)
   */
  private calculateHumanCost(step: any): number {
    // Simplified cost calculation for human review
    const hourlyRate = 50; // $50 per hour
    const durationHours = (step.duration || 0) / (1000 * 60 * 60);
    return hourlyRate * durationHours;
  }

  /**
   * Calculate infrastructure cost (simplified)
   */
  private calculateInfrastructureCost(execution: any): number {
    // Simplified infrastructure cost
    const baseCost = 0.001; // Base infrastructure cost per execution
    const stepCount = Object.keys(execution.stepResults || {}).length;
    return baseCost * stepCount;
  }

  /**
   * Calculate cost for individual step
   */
  private calculateStepCost(step: any): number {
    switch (step.stepType) {
      case 'ai_generation':
        return this.calculateAICost(step);
      case 'human_review':
        return this.calculateHumanCost(step);
      default:
        return 0.001; // Minimal cost for other steps
    }
  }

  /**
   * Generate trend data for visualization
   */
  generateTrendData(executions: any[], metric: string, timeRange: string = '30d'): TrendData[] {
    const trendData: TrendData[] = [];
    
    executions.forEach(execution => {
      if (!execution.startedAt) return;

      let value = 0;
      switch (metric) {
        case 'quality':
          value = this.extractQualityScore(execution);
          break;
        case 'duration':
          value = this.extractExecutionDuration(execution);
          break;
        case 'cost':
          value = this.calculateExecutionCost(execution);
          break;
        default:
          value = 0;
      }

      trendData.push({
        timestamp: execution.startedAt,
        value,
        label: execution.id
      });
    });

    return trendData.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }

  /**
   * Extract quality score from execution
   */
  private extractQualityScore(execution: any): number {
    const stepResults = Object.values(execution.stepResults || {});
    const qualityScores = stepResults
      .map((step: any) => step.outputs?.seo_analysis?.score || step.outputs?.quality_score)
      .filter(score => score !== undefined);

    return qualityScores.length > 0
      ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
      : 0;
  }

  /**
   * Extract execution duration
   */
  private extractExecutionDuration(execution: any): number {
    if (!execution.startedAt || !execution.completedAt) return 0;
    return new Date(execution.completedAt).getTime() - new Date(execution.startedAt).getTime();
  }

  /**
   * Calculate total execution cost
   */
  private calculateExecutionCost(execution: any): number {
    const stepResults = Object.values(execution.stepResults || {});
    return stepResults.reduce((total, step: any) => total + this.calculateStepCost(step), 0);
  }
}
