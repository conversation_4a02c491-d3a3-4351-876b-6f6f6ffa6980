/**
 * Real-time Broadcaster
 * Manages real-time event broadcasting for workflow progress updates
 */

export interface ClientConnection {
  executionId?: string;
  connectedAt: string;
  lastActivity?: string;
  cleanup?: () => void;
}

export interface BroadcastEvent {
  type: string;
  data: any;
  timestamp: string;
  executionId?: string;
}

export type EventHandler = (data: any) => void;

export class RealTimeBroadcaster {
  private clients: Map<string, ClientConnection> = new Map();
  private eventHandlers: Map<string, Set<EventHandler>> = new Map();
  private eventHistory: BroadcastEvent[] = [];
  private maxHistorySize = 100;

  /**
   * Add a client connection
   */
  addClient(clientId: string, connection: ClientConnection): void {
    console.log(`📡 Adding client ${clientId} to broadcaster`);
    this.clients.set(clientId, {
      ...connection,
      lastActivity: new Date().toISOString()
    });

    // Send recent events to new client if they're for the same execution
    if (connection.executionId) {
      const recentEvents = this.getRecentEvents(connection.executionId, 10);
      if (recentEvents.length > 0) {
        console.log(`📤 Sending ${recentEvents.length} recent events to client ${clientId}`);
      }
    }
  }

  /**
   * Remove a client connection
   */
  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      console.log(`📡 Removing client ${clientId} from broadcaster`);
      
      // Call cleanup function if provided
      if (client.cleanup) {
        try {
          client.cleanup();
        } catch (error) {
          console.error(`❌ Error during client cleanup for ${clientId}:`, error);
        }
      }
      
      this.clients.delete(clientId);
    }
  }

  /**
   * Get all connected clients
   */
  getClients(): Map<string, ClientConnection> {
    return new Map(this.clients);
  }

  /**
   * Get clients for specific execution
   */
  getClientsForExecution(executionId: string): Map<string, ClientConnection> {
    const executionClients = new Map<string, ClientConnection>();
    
    for (const [clientId, client] of this.clients.entries()) {
      if (client.executionId === executionId) {
        executionClients.set(clientId, client);
      }
    }
    
    return executionClients;
  }

  /**
   * Register event handler
   */
  on(eventType: string, handler: EventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    this.eventHandlers.get(eventType)!.add(handler);
  }

  /**
   * Unregister event handler
   */
  off(eventType: string, handler: EventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
      }
    }
  }

  /**
   * Broadcast event to all relevant clients
   */
  broadcast(eventType: string, data: any): void {
    const event: BroadcastEvent = {
      type: eventType,
      data,
      timestamp: new Date().toISOString(),
      executionId: data.executionId
    };

    // Add to event history
    this.addToHistory(event);

    // Emit to registered handlers
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`❌ Error in event handler for ${eventType}:`, error);
        }
      });
    }

    // Log broadcast
    const clientCount = this.clients.size;
    const executionClients = data.executionId ? this.getClientsForExecution(data.executionId).size : 0;
    
    console.log(`📡 Broadcasting ${eventType} to ${clientCount} total clients (${executionClients} for execution ${data.executionId || 'all'})`);
  }

  /**
   * Broadcast to specific execution
   */
  broadcastToExecution(executionId: string, eventType: string, data: any): void {
    this.broadcast(eventType, {
      ...data,
      executionId
    });
  }

  /**
   * Broadcast workflow started event
   */
  broadcastWorkflowStarted(executionId: string, workflowId: string, additionalData?: any): void {
    this.broadcast('workflow_started', {
      executionId,
      workflowId,
      startedAt: new Date().toISOString(),
      ...additionalData
    });
  }

  /**
   * Broadcast workflow completed event
   */
  broadcastWorkflowCompleted(executionId: string, workflowId: string, duration: number, additionalData?: any): void {
    this.broadcast('workflow_completed', {
      executionId,
      workflowId,
      completedAt: new Date().toISOString(),
      duration,
      ...additionalData
    });
  }

  /**
   * Broadcast workflow failed event
   */
  broadcastWorkflowFailed(executionId: string, workflowId: string, error: any, additionalData?: any): void {
    this.broadcast('workflow_failed', {
      executionId,
      workflowId,
      failedAt: new Date().toISOString(),
      error: {
        message: error instanceof Error ? error.message : String(error),
        code: error.code || 'WORKFLOW_FAILED'
      },
      ...additionalData
    });
  }

  /**
   * Broadcast workflow paused event
   */
  broadcastWorkflowPaused(executionId: string, reason: string, additionalData?: any): void {
    this.broadcast('workflow_paused', {
      executionId,
      pausedAt: new Date().toISOString(),
      reason,
      ...additionalData
    });
  }

  /**
   * Broadcast step started event
   */
  broadcastStepStarted(executionId: string, stepId: string, stepName: string, additionalData?: any): void {
    this.broadcast('step_started', {
      executionId,
      stepId,
      stepName,
      startedAt: new Date().toISOString(),
      ...additionalData
    });
  }

  /**
   * Broadcast step completed event
   */
  broadcastStepCompleted(executionId: string, stepId: string, stepName: string, outputs?: any, additionalData?: any): void {
    this.broadcast('step_completed', {
      executionId,
      stepId,
      stepName,
      completedAt: new Date().toISOString(),
      outputs,
      ...additionalData
    });
  }

  /**
   * Broadcast step failed event
   */
  broadcastStepFailed(executionId: string, stepId: string, stepName: string, error: any, additionalData?: any): void {
    this.broadcast('step_failed', {
      executionId,
      stepId,
      stepName,
      failedAt: new Date().toISOString(),
      error: {
        message: error instanceof Error ? error.message : String(error),
        code: error.code || 'STEP_FAILED'
      },
      ...additionalData
    });
  }

  /**
   * Broadcast progress update
   */
  broadcastProgressUpdate(executionId: string, progress: number, currentStep?: string, additionalData?: any): void {
    this.broadcast('progress_update', {
      executionId,
      progress,
      currentStep,
      updatedAt: new Date().toISOString(),
      ...additionalData
    });
  }

  /**
   * Broadcast review requested event
   */
  broadcastReviewRequested(executionId: string, stepId: string, artifactId: string, additionalData?: any): void {
    this.broadcast('review_requested', {
      executionId,
      stepId,
      artifactId,
      requestedAt: new Date().toISOString(),
      ...additionalData
    });
  }

  /**
   * Broadcast review completed event
   */
  broadcastReviewCompleted(executionId: string, stepId: string, decision: string, additionalData?: any): void {
    this.broadcast('review_completed', {
      executionId,
      stepId,
      decision,
      completedAt: new Date().toISOString(),
      ...additionalData
    });
  }

  /**
   * Add event to history
   */
  private addToHistory(event: BroadcastEvent): void {
    this.eventHistory.push(event);
    
    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Get recent events for an execution
   */
  getRecentEvents(executionId?: string, limit: number = 10): BroadcastEvent[] {
    let events = this.eventHistory;
    
    // Filter by execution ID if provided
    if (executionId) {
      events = events.filter(event => event.executionId === executionId);
    }
    
    // Return most recent events
    return events.slice(-limit);
  }

  /**
   * Get broadcaster statistics
   */
  getStats(): {
    totalClients: number;
    clientsByExecution: Record<string, number>;
    totalEvents: number;
    eventsByType: Record<string, number>;
    uptime: number;
  } {
    const clientsByExecution: Record<string, number> = {};
    
    for (const client of this.clients.values()) {
      if (client.executionId) {
        clientsByExecution[client.executionId] = (clientsByExecution[client.executionId] || 0) + 1;
      }
    }

    const eventsByType: Record<string, number> = {};
    for (const event of this.eventHistory) {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    }

    return {
      totalClients: this.clients.size,
      clientsByExecution,
      totalEvents: this.eventHistory.length,
      eventsByType,
      uptime: process.uptime() * 1000 // Convert to milliseconds
    };
  }

  /**
   * Clean up inactive clients
   */
  cleanupInactiveClients(maxInactiveTime: number = 5 * 60 * 1000): void {
    const now = Date.now();
    const inactiveClients: string[] = [];

    for (const [clientId, client] of this.clients.entries()) {
      const lastActivity = new Date(client.lastActivity || client.connectedAt).getTime();
      if (now - lastActivity > maxInactiveTime) {
        inactiveClients.push(clientId);
      }
    }

    inactiveClients.forEach(clientId => {
      console.log(`🧹 Removing inactive client: ${clientId}`);
      this.removeClient(clientId);
    });

    if (inactiveClients.length > 0) {
      console.log(`🧹 Cleaned up ${inactiveClients.length} inactive clients`);
    }
  }

  /**
   * Update client activity
   */
  updateClientActivity(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.lastActivity = new Date().toISOString();
    }
  }
}

// Singleton instance
let broadcasterInstance: RealTimeBroadcaster | null = null;

/**
 * Get singleton broadcaster instance
 */
export function getRealTimeBroadcaster(): RealTimeBroadcaster {
  if (!broadcasterInstance) {
    broadcasterInstance = new RealTimeBroadcaster();
    
    // Set up periodic cleanup
    setInterval(() => {
      broadcasterInstance?.cleanupInactiveClients();
    }, 60000); // Every minute
  }
  
  return broadcasterInstance;
}
