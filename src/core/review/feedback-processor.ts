/**
 * Feedback Processor
 * Analyzes human feedback and determines appropriate actions for content improvement
 */

export interface FeedbackAnalysis {
  isActionable: boolean;
  sentiment: 'positive' | 'negative' | 'neutral';
  categories: FeedbackCategory[];
  suggestedAction: 'approve' | 'regenerate' | 'manual_review' | 'reject';
  improvementAreas: ImprovementArea[];
  priority: 'high' | 'medium' | 'low';
  estimatedImprovementTime: number; // in seconds
  confidence: number; // 0-1
  regenerationStrategy?: RegenerationStrategy;
}

export interface FeedbackCategory {
  type: 'content' | 'style' | 'accuracy' | 'seo' | 'structure' | 'tone' | 'technical';
  severity: 'critical' | 'major' | 'minor';
  description: string;
  examples: string[];
}

export interface ImprovementArea {
  area: string;
  description: string;
  suggestions: string[];
  priority: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
}

export interface RegenerationStrategy {
  approach: 'full_rewrite' | 'targeted_improvements' | 'style_adjustment' | 'fact_correction';
  focusAreas: string[];
  preserveElements: string[];
  additionalInstructions: string[];
  maxAttempts: number;
}

export interface ProcessedFeedback {
  originalFeedback: string;
  analysis: FeedbackAnalysis;
  actionPlan: ActionPlan;
  regenerationPrompt?: string;
  metadata: {
    processedAt: string;
    processingTime: number;
    version: string;
  };
}

export interface ActionPlan {
  primaryAction: 'approve' | 'regenerate' | 'manual_review' | 'reject';
  secondaryActions: string[];
  timeline: string;
  resources: string[];
  successCriteria: string[];
}

export class FeedbackProcessor {
  private version = '1.0.0';

  /**
   * Process human feedback and generate analysis
   */
  async processFeedback(
    feedback: string,
    originalContent: string,
    contentType: string = 'blog_post'
  ): Promise<ProcessedFeedback> {
    console.log(`🔍 Processing feedback for ${contentType}...`);
    const startTime = Date.now();

    // Analyze the feedback
    const analysis = await this.analyzeFeedback(feedback, originalContent, contentType);

    // Generate action plan
    const actionPlan = this.generateActionPlan(analysis);

    // Generate regeneration prompt if needed
    let regenerationPrompt: string | undefined;
    if (analysis.suggestedAction === 'regenerate') {
      regenerationPrompt = this.generateRegenerationPrompt(feedback, analysis, originalContent);
    }

    const processingTime = Date.now() - startTime;

    const processedFeedback: ProcessedFeedback = {
      originalFeedback: feedback,
      analysis,
      actionPlan,
      regenerationPrompt,
      metadata: {
        processedAt: new Date().toISOString(),
        processingTime,
        version: this.version
      }
    };

    console.log(`✅ Feedback processed. Action: ${analysis.suggestedAction}, Confidence: ${analysis.confidence}`);
    return processedFeedback;
  }

  /**
   * Analyze feedback content and determine actionability
   */
  private async analyzeFeedback(
    feedback: string,
    originalContent: string,
    contentType: string
  ): Promise<FeedbackAnalysis> {
    // Normalize feedback
    const normalizedFeedback = feedback.toLowerCase().trim();

    // Determine sentiment
    const sentiment = this.analyzeSentiment(normalizedFeedback);

    // Categorize feedback
    const categories = this.categorizeFeedback(normalizedFeedback);

    // Determine if feedback is actionable
    const isActionable = this.isActionableFeedback(normalizedFeedback, categories);

    // Suggest action based on analysis
    const suggestedAction = this.suggestAction(sentiment, categories, isActionable);

    // Identify improvement areas
    const improvementAreas = this.identifyImprovementAreas(normalizedFeedback, categories);

    // Determine priority
    const priority = this.determinePriority(categories, sentiment);

    // Estimate improvement time
    const estimatedImprovementTime = this.estimateImprovementTime(categories, improvementAreas);

    // Calculate confidence
    const confidence = this.calculateConfidence(feedback, categories, isActionable);

    // Generate regeneration strategy if needed
    let regenerationStrategy: RegenerationStrategy | undefined;
    if (suggestedAction === 'regenerate') {
      regenerationStrategy = this.generateRegenerationStrategy(categories, improvementAreas);
    }

    return {
      isActionable,
      sentiment,
      categories,
      suggestedAction,
      improvementAreas,
      priority,
      estimatedImprovementTime,
      confidence,
      regenerationStrategy
    };
  }

  /**
   * Analyze sentiment of feedback
   */
  private analyzeSentiment(feedback: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['good', 'great', 'excellent', 'perfect', 'love', 'like', 'approve', 'accept'];
    const negativeWords = ['bad', 'poor', 'terrible', 'wrong', 'hate', 'dislike', 'reject', 'awful', 'needs work'];

    const words = feedback.split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
      if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Categorize feedback into different types
   */
  private categorizeFeedback(feedback: string): FeedbackCategory[] {
    const categories: FeedbackCategory[] = [];

    // Content-related feedback
    if (this.containsKeywords(feedback, ['content', 'information', 'facts', 'data', 'details'])) {
      categories.push({
        type: 'content',
        severity: this.determineSeverity(feedback, ['wrong', 'incorrect', 'missing']),
        description: 'Feedback about content accuracy and completeness',
        examples: this.extractExamples(feedback, ['content', 'information'])
      });
    }

    // Style-related feedback
    if (this.containsKeywords(feedback, ['style', 'tone', 'voice', 'writing', 'flow'])) {
      categories.push({
        type: 'style',
        severity: this.determineSeverity(feedback, ['confusing', 'unclear', 'boring']),
        description: 'Feedback about writing style and tone',
        examples: this.extractExamples(feedback, ['style', 'tone'])
      });
    }

    // SEO-related feedback
    if (this.containsKeywords(feedback, ['seo', 'keywords', 'meta', 'title', 'description', 'search'])) {
      categories.push({
        type: 'seo',
        severity: this.determineSeverity(feedback, ['missing', 'poor', 'bad']),
        description: 'Feedback about SEO optimization',
        examples: this.extractExamples(feedback, ['seo', 'keywords'])
      });
    }

    // Structure-related feedback
    if (this.containsKeywords(feedback, ['structure', 'organization', 'headings', 'sections', 'format'])) {
      categories.push({
        type: 'structure',
        severity: this.determineSeverity(feedback, ['confusing', 'messy', 'unclear']),
        description: 'Feedback about content structure and organization',
        examples: this.extractExamples(feedback, ['structure', 'organization'])
      });
    }

    // Accuracy-related feedback
    if (this.containsKeywords(feedback, ['accuracy', 'facts', 'correct', 'verify', 'check'])) {
      categories.push({
        type: 'accuracy',
        severity: this.determineSeverity(feedback, ['wrong', 'incorrect', 'false']),
        description: 'Feedback about factual accuracy',
        examples: this.extractExamples(feedback, ['accuracy', 'facts'])
      });
    }

    return categories;
  }

  /**
   * Check if feedback contains specific keywords
   */
  private containsKeywords(feedback: string, keywords: string[]): boolean {
    return keywords.some(keyword => feedback.includes(keyword));
  }

  /**
   * Determine severity based on negative indicators
   */
  private determineSeverity(feedback: string, negativeIndicators: string[]): 'critical' | 'major' | 'minor' {
    const criticalWords = ['critical', 'urgent', 'must', 'required', 'essential'];
    const majorWords = ['important', 'significant', 'should', 'needs'];

    if (this.containsKeywords(feedback, criticalWords)) return 'critical';
    if (this.containsKeywords(feedback, majorWords) || this.containsKeywords(feedback, negativeIndicators)) return 'major';
    return 'minor';
  }

  /**
   * Extract examples from feedback
   */
  private extractExamples(feedback: string, keywords: string[]): string[] {
    const sentences = feedback.split(/[.!?]+/);
    return sentences
      .filter(sentence => keywords.some(keyword => sentence.includes(keyword)))
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
      .slice(0, 3); // Limit to 3 examples
  }

  /**
   * Determine if feedback is actionable
   */
  private isActionableFeedback(feedback: string, categories: FeedbackCategory[]): boolean {
    // Check for specific, actionable language
    const actionableIndicators = [
      'change', 'add', 'remove', 'improve', 'fix', 'update', 'revise',
      'include', 'exclude', 'modify', 'adjust', 'enhance', 'correct'
    ];

    const hasActionableLanguage = this.containsKeywords(feedback, actionableIndicators);
    const hasSpecificCategories = categories.length > 0;
    const hasSubstantialContent = feedback.length > 20;

    return hasActionableLanguage && hasSpecificCategories && hasSubstantialContent;
  }

  /**
   * Suggest action based on analysis
   */
  private suggestAction(
    sentiment: string,
    categories: FeedbackCategory[],
    isActionable: boolean
  ): 'approve' | 'regenerate' | 'manual_review' | 'reject' {
    if (!isActionable) {
      return sentiment === 'positive' ? 'approve' : 'manual_review';
    }

    const hasCriticalIssues = categories.some(cat => cat.severity === 'critical');
    const hasMajorIssues = categories.some(cat => cat.severity === 'major');

    if (hasCriticalIssues) {
      return 'regenerate';
    }

    if (hasMajorIssues && sentiment === 'negative') {
      return 'regenerate';
    }

    if (sentiment === 'positive') {
      return 'approve';
    }

    return 'manual_review';
  }

  /**
   * Identify improvement areas
   */
  private identifyImprovementAreas(feedback: string, categories: FeedbackCategory[]): ImprovementArea[] {
    const areas: ImprovementArea[] = [];

    categories.forEach(category => {
      const area: ImprovementArea = {
        area: category.type,
        description: category.description,
        suggestions: this.generateSuggestions(category.type, feedback),
        priority: category.severity === 'critical' ? 'high' : category.severity === 'major' ? 'medium' : 'low',
        effort: this.estimateEffort(category.type, category.severity)
      };
      areas.push(area);
    });

    return areas;
  }

  /**
   * Generate suggestions for improvement
   */
  private generateSuggestions(categoryType: string, feedback: string): string[] {
    const suggestions: Record<string, string[]> = {
      content: [
        'Add more detailed information',
        'Include relevant examples',
        'Verify facts and data',
        'Expand on key points'
      ],
      style: [
        'Adjust tone to match audience',
        'Improve sentence flow',
        'Use more engaging language',
        'Simplify complex sentences'
      ],
      seo: [
        'Optimize keyword density',
        'Improve meta descriptions',
        'Add relevant headings',
        'Include internal links'
      ],
      structure: [
        'Reorganize content sections',
        'Add clear headings',
        'Improve logical flow',
        'Use bullet points for clarity'
      ],
      accuracy: [
        'Fact-check all claims',
        'Add credible sources',
        'Verify statistics',
        'Update outdated information'
      ]
    };

    return suggestions[categoryType] || ['Review and improve based on feedback'];
  }

  /**
   * Estimate effort required for improvements
   */
  private estimateEffort(categoryType: string, severity: string): 'low' | 'medium' | 'high' {
    if (severity === 'critical') return 'high';
    if (severity === 'major') return 'medium';
    return 'low';
  }

  /**
   * Determine priority of feedback
   */
  private determinePriority(categories: FeedbackCategory[], sentiment: string): 'high' | 'medium' | 'low' {
    const hasCritical = categories.some(cat => cat.severity === 'critical');
    const hasMajor = categories.some(cat => cat.severity === 'major');

    if (hasCritical) return 'high';
    if (hasMajor && sentiment === 'negative') return 'high';
    if (hasMajor) return 'medium';
    return 'low';
  }

  /**
   * Estimate time needed for improvements
   */
  private estimateImprovementTime(categories: FeedbackCategory[], areas: ImprovementArea[]): number {
    let baseTime = 30; // 30 seconds base time

    categories.forEach(category => {
      switch (category.severity) {
        case 'critical':
          baseTime += 120; // 2 minutes
          break;
        case 'major':
          baseTime += 60; // 1 minute
          break;
        case 'minor':
          baseTime += 30; // 30 seconds
          break;
      }
    });

    return Math.min(baseTime, 300); // Cap at 5 minutes
  }

  /**
   * Calculate confidence in analysis
   */
  private calculateConfidence(feedback: string, categories: FeedbackCategory[], isActionable: boolean): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence for longer, more detailed feedback
    if (feedback.length > 100) confidence += 0.2;
    if (feedback.length > 200) confidence += 0.1;

    // Increase confidence for categorized feedback
    confidence += categories.length * 0.1;

    // Increase confidence for actionable feedback
    if (isActionable) confidence += 0.2;

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate regeneration strategy
   */
  private generateRegenerationStrategy(
    categories: FeedbackCategory[],
    areas: ImprovementArea[]
  ): RegenerationStrategy {
    const hasCriticalContent = categories.some(cat => cat.type === 'content' && cat.severity === 'critical');
    const hasStructuralIssues = categories.some(cat => cat.type === 'structure');

    let approach: RegenerationStrategy['approach'] = 'targeted_improvements';
    
    if (hasCriticalContent) {
      approach = 'full_rewrite';
    } else if (hasStructuralIssues) {
      approach = 'targeted_improvements';
    } else if (categories.some(cat => cat.type === 'style')) {
      approach = 'style_adjustment';
    } else if (categories.some(cat => cat.type === 'accuracy')) {
      approach = 'fact_correction';
    }

    const focusAreas = areas.filter(area => area.priority === 'high').map(area => area.area);
    const preserveElements = approach === 'full_rewrite' ? [] : ['structure', 'key_points'];

    return {
      approach,
      focusAreas,
      preserveElements,
      additionalInstructions: areas.flatMap(area => area.suggestions).slice(0, 5),
      maxAttempts: approach === 'full_rewrite' ? 2 : 3
    };
  }

  /**
   * Generate action plan
   */
  private generateActionPlan(analysis: FeedbackAnalysis): ActionPlan {
    const primaryAction = analysis.suggestedAction;
    const secondaryActions: string[] = [];

    if (primaryAction === 'regenerate') {
      secondaryActions.push('Review regenerated content');
      secondaryActions.push('Compare with original');
      if (analysis.regenerationStrategy?.maxAttempts && analysis.regenerationStrategy.maxAttempts > 1) {
        secondaryActions.push('Prepare for potential re-regeneration');
      }
    } else if (primaryAction === 'manual_review') {
      secondaryActions.push('Escalate to human reviewer');
      secondaryActions.push('Provide detailed feedback context');
    }

    const timeline = this.generateTimeline(analysis);
    const resources = this.generateResources(analysis);
    const successCriteria = this.generateSuccessCriteria(analysis);

    return {
      primaryAction,
      secondaryActions,
      timeline,
      resources,
      successCriteria
    };
  }

  /**
   * Generate timeline for action plan
   */
  private generateTimeline(analysis: FeedbackAnalysis): string {
    const time = analysis.estimatedImprovementTime;
    if (time < 60) return `${time} seconds`;
    if (time < 3600) return `${Math.round(time / 60)} minutes`;
    return `${Math.round(time / 3600)} hours`;
  }

  /**
   * Generate resources needed
   */
  private generateResources(analysis: FeedbackAnalysis): string[] {
    const resources = ['AI content generation'];
    
    if (analysis.categories.some(cat => cat.type === 'accuracy')) {
      resources.push('Fact-checking tools');
    }
    
    if (analysis.categories.some(cat => cat.type === 'seo')) {
      resources.push('SEO analysis tools');
    }
    
    if (analysis.priority === 'high') {
      resources.push('Priority processing queue');
    }

    return resources;
  }

  /**
   * Generate success criteria
   */
  private generateSuccessCriteria(analysis: FeedbackAnalysis): string[] {
    const criteria = ['Address all identified issues'];
    
    analysis.improvementAreas.forEach(area => {
      if (area.priority === 'high') {
        criteria.push(`Improve ${area.area} quality`);
      }
    });

    if (analysis.categories.some(cat => cat.severity === 'critical')) {
      criteria.push('Resolve all critical issues');
    }

    criteria.push('Maintain content quality standards');
    
    return criteria;
  }

  /**
   * Generate regeneration prompt
   */
  private generateRegenerationPrompt(
    feedback: string,
    analysis: FeedbackAnalysis,
    originalContent: string
  ): string {
    let prompt = `Please improve the following content based on this feedback:\n\nFeedback: "${feedback}"\n\n`;
    
    if (analysis.regenerationStrategy) {
      const strategy = analysis.regenerationStrategy;
      
      prompt += `Improvement Strategy: ${strategy.approach.replace('_', ' ')}\n`;
      
      if (strategy.focusAreas.length > 0) {
        prompt += `Focus on: ${strategy.focusAreas.join(', ')}\n`;
      }
      
      if (strategy.preserveElements.length > 0) {
        prompt += `Preserve: ${strategy.preserveElements.join(', ')}\n`;
      }
      
      if (strategy.additionalInstructions.length > 0) {
        prompt += `Additional instructions:\n${strategy.additionalInstructions.map(inst => `- ${inst}`).join('\n')}\n`;
      }
    }
    
    prompt += `\nOriginal Content:\n${originalContent}\n\n`;
    prompt += `Please provide the improved version:`;
    
    return prompt;
  }
}
